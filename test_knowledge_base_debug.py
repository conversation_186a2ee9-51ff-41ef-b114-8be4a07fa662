#!/usr/bin/env python3
"""
Test script for Knowledge Base debugging functionality.

This script tests the debugging features we've added to the import process
to help identify where the freezing occurs.
"""

import sys
import time
import psutil
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from PyQt6.QtWidgets import QApplication
from knowledge_base.ui.knowledge_base_panel import KnowledgeBasePanel


def test_memory_monitoring():
    """Test the memory monitoring functionality."""
    print("=== Testing Memory Monitoring ===")
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"Initial memory: {initial_memory:.2f} MB")
    
    # Create some test data
    test_data = []
    for i in range(5):
        data_chunk = "x" * 100000  # 100KB chunks
        test_data.append(data_chunk)
        
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_delta = current_memory - initial_memory
        print(f"After chunk {i+1}: {current_memory:.2f} MB (delta: {memory_delta:+.2f} MB)")
    
    # Clean up
    del test_data
    
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    final_delta = final_memory - initial_memory
    print(f"After cleanup: {final_memory:.2f} MB (delta: {final_delta:+.2f} MB)")


def test_document_format_simulation():
    """Test the document format simulation."""
    print("\n=== Testing Document Format Simulation ===")
    
    class MockDocumentFormat:
        def __init__(self, size_multiplier=1):
            self.name = f"Test Document (size x{size_multiplier})"
            self.html_content = f"<html><body><p>{'Test content. ' * (1000 * size_multiplier)}</p></body></html>"
            self.content = f"Plain text content. " * (5000 * size_multiplier)
            self.id = f"test_doc_{size_multiplier}"
    
    # Test different document sizes
    for multiplier in [1, 5, 10]:
        mock_doc = MockDocumentFormat(multiplier)
        
        html_size = len(mock_doc.html_content)
        content_size = len(mock_doc.content)
        
        print(f"Document: {mock_doc.name}")
        print(f"  HTML size: {html_size:,} characters ({html_size/1024:.2f} KB)")
        print(f"  Content size: {content_size:,} characters ({content_size/1024:.2f} KB)")
        
        # Check against our limits
        if html_size > 200000:
            print(f"  WARNING: HTML exceeds 200KB limit")
        if content_size > 500000:
            print(f"  WARNING: Content exceeds 500KB limit")


def test_timing_measurements():
    """Test timing measurement functionality."""
    print("\n=== Testing Timing Measurements ===")
    
    operations = [
        ("Quick operation", lambda: time.sleep(0.01)),
        ("Medium operation", lambda: time.sleep(0.1)),
        ("Slow operation", lambda: time.sleep(0.5)),
    ]
    
    for name, operation in operations:
        start_time = time.time()
        operation()
        duration = time.time() - start_time
        print(f"{name}: {duration:.3f} seconds")


def test_knowledge_base_panel():
    """Test the Knowledge Base panel functionality."""
    print("\n=== Testing Knowledge Base Panel ===")
    
    app = QApplication(sys.argv)
    
    try:
        # Create the panel
        panel = KnowledgeBasePanel()
        panel.show()
        
        print("Knowledge Base panel created successfully")
        print("Panel components:")
        print(f"  - Tab widget with {panel.tab_widget.count()} tabs")
        print(f"  - Import log widget: {type(panel.import_log).__name__}")
        print(f"  - Debug output widget: {type(panel.debug_output).__name__}")
        
        # Test the debug functionality
        panel._check_memory_usage()
        panel._test_import_process()
        
        print("Debug functionality tested successfully")
        
        # Don't actually show the GUI in test mode
        panel.close()
        
    except Exception as e:
        print(f"Error testing Knowledge Base panel: {e}")
        logger.error(f"Knowledge Base panel test failed: {e}")
    
    finally:
        app.quit()


def main():
    """Run all tests."""
    print("Knowledge Base Debugging Test Suite")
    print("=" * 50)
    
    try:
        test_memory_monitoring()
        test_document_format_simulation()
        test_timing_measurements()
        test_knowledge_base_panel()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("\nThe debugging features are ready to help identify freezing issues.")
        print("When you experience freezing during import:")
        print("1. Check the application logs for detailed timing information")
        print("2. Use the Knowledge Base panel's debug tab to monitor memory")
        print("3. Look for the specific step where timing stops in the logs")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        logger.error(f"Test suite error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    # Configure logging for testing
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
