#!/usr/bin/env python3
"""
Test script to verify the z-ordering fix for dialog boxes.

This script tests that message boxes appear on top of windows with WindowStaysOnTopHint.
"""

import sys
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QMessageBox, QLabel


class TestWindow(QMainWindow):
    """Test window with WindowStaysOnTopHint to simulate RTF Editor behavior."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test RTF Editor (Stays On Top)")
        self.setGeometry(100, 100, 600, 400)
        
        # Set the window to stay on top (like RTF Editor)
        self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add some content
        label = QLabel("This window has WindowStaysOnTopHint set.\n"
                      "It should stay on top of other windows.")
        label.setStyleSheet("font-size: 14px; padding: 20px;")
        layout.addWidget(label)
        
        # Test buttons
        test_old_btn = QPushButton("Test OLD Dialog (will be hidden)")
        test_old_btn.clicked.connect(self.test_old_dialog)
        layout.addWidget(test_old_btn)
        
        test_new_btn = QPushButton("Test NEW Dialog (should appear on top)")
        test_new_btn.clicked.connect(self.test_new_dialog)
        layout.addWidget(test_new_btn)
        
        # Auto-test button
        auto_test_btn = QPushButton("Run Automatic Test")
        auto_test_btn.clicked.connect(self.run_auto_test)
        layout.addWidget(auto_test_btn)
        
        self.show()
    
    def test_old_dialog(self):
        """Test the old way of showing dialogs (will be hidden behind this window)."""
        QMessageBox.information(
            self,
            "Old Dialog",
            "This dialog uses the old method and will be hidden behind the main window."
        )
    
    def test_new_dialog(self):
        """Test the new way of showing dialogs (should appear on top)."""
        # Create message box with proper z-ordering
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("New Dialog")
        msg_box.setText("This dialog uses the new method with WindowStaysOnTopHint.\n"
                       "It should appear on top of the main window.")
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        
        # Ensure the dialog stays on top
        msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        
        # Show the dialog
        msg_box.exec()
    
    def run_auto_test(self):
        """Run an automatic test sequence."""
        print("Starting automatic test sequence...")
        
        # Test 1: Show old dialog after delay
        QTimer.singleShot(1000, lambda: self.show_test_dialog("old"))
        
        # Test 2: Show new dialog after delay
        QTimer.singleShot(3000, lambda: self.show_test_dialog("new"))
        
        print("Automatic test scheduled. Watch for dialogs in 1 and 3 seconds.")
    
    def show_test_dialog(self, dialog_type):
        """Show a test dialog of the specified type."""
        if dialog_type == "old":
            print("Showing OLD dialog (should be hidden)...")
            QMessageBox.information(
                self,
                "Auto Test - Old Dialog",
                "This is the OLD dialog method.\nIt should be hidden behind the main window.\n\n"
                "If you can see this dialog, the z-ordering issue still exists."
            )
        else:
            print("Showing NEW dialog (should be visible)...")
            # Create message box with proper z-ordering
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Auto Test - New Dialog")
            msg_box.setText("This is the NEW dialog method.\nIt should appear on top of the main window.\n\n"
                           "If you can see this dialog, the z-ordering fix is working!")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            
            # Ensure the dialog stays on top
            msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            
            # Show the dialog
            msg_box.exec()


def main():
    """Run the z-ordering test."""
    print("Z-Ordering Fix Test")
    print("=" * 50)
    print("This test verifies that dialog boxes appear on top of windows with WindowStaysOnTopHint.")
    print()
    print("Instructions:")
    print("1. A test window will open with WindowStaysOnTopHint (like RTF Editor)")
    print("2. Click 'Test OLD Dialog' - this dialog should be hidden behind the window")
    print("3. Click 'Test NEW Dialog' - this dialog should appear on top")
    print("4. Use 'Run Automatic Test' to see both dialogs automatically")
    print()
    print("If the NEW dialog appears on top, the z-ordering fix is working!")
    print()
    
    app = QApplication(sys.argv)
    
    try:
        # Create and show the test window
        test_window = TestWindow()
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"Test failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
