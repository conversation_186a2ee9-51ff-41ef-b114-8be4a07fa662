#!/usr/bin/env python3
"""
Test script to verify document conversion and save functionality fixes.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("Testing Document Conversion and Save Fixes...")

def test_metadata_validation():
    """Test that metadata validation works correctly."""
    print("=== Testing Metadata Validation ===")
    
    try:
        from document_manager.models.document import Document, DocumentType
        from datetime import datetime
        from pathlib import Path
        import uuid
        
        # Test creating a document with the fixed metadata structure
        metadata = {
            'converted_document': True,
            'conversion_id': str(uuid.uuid4()),
            'has_images': False,
            'image_count': 0,
            'has_tables': True,
            'table_count': 2,
            'html_content_length': 1000,
            'conversion_metadata_json': '{"original_format": ".docx", "tool": "test"}'
        }
        
        document = Document(
            id=str(uuid.uuid4()),
            name="Test Document",
            file_path=Path("test.docx"),
            file_type=DocumentType.DOCX,
            size_bytes=1000,
            creation_date=datetime.now(),
            last_modified_date=datetime.now(),
            content="<html><body>Test</body></html>",
            extracted_text="Test",
            tags=set(),
            category=None,
            notes=None,
            word_count=1,
            metadata=metadata
        )
        
        print("✅ Successfully created Document with fixed metadata structure")
        print(f"   Document ID: {document.id}")
        print(f"   Metadata keys: {list(document.metadata.keys())}")
        print(f"   Metadata types: {[(k, type(v).__name__) for k, v in document.metadata.items()]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing metadata validation: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_converted_document_service():
    """Test the ConvertedDocumentService with fixed metadata handling."""
    print("\n=== Testing ConvertedDocumentService ===")
    
    try:
        from document_manager.services.converted_document_service import ConvertedDocumentService
        from shared.ui.widgets.rtf_editor.models.document_format import DocumentFormat
        import uuid
        
        # Create a test DocumentFormat
        doc_format = DocumentFormat(
            id=str(uuid.uuid4()),
            name="Test Document",
            html_content="<html><body><h1>Test</h1><p>This is a test document.</p></body></html>",
            plain_text="Test\nThis is a test document.",
            created_at=datetime.now(),
            modified_at=datetime.now(),
            word_count=6,
            character_count=100,
            metadata={
                'original_format': '.docx',
                'conversion_tool': 'test',
                'has_images': False
            }
        )
        
        print(f"✅ Created test DocumentFormat: {doc_format.name}")
        
        # Test the service
        service = ConvertedDocumentService()
        print("✅ Created ConvertedDocumentService")
        
        # Test document conversion (without saving to avoid database issues)
        document = service._document_format_to_document(
            doc_format,
            original_file_path="test.docx",
            category="Test Category",
            tags=["test", "document"],
            notes="Test notes"
        )
        
        print(f"✅ Converted DocumentFormat to Document")
        print(f"   Document ID: {document.id}")
        print(f"   Document name: {document.name}")
        print(f"   Metadata keys: {list(document.metadata.keys())}")
        print(f"   Has conversion metadata: {'conversion_metadata_json' in document.metadata}")
        
        # Check that metadata is properly formatted
        if 'conversion_metadata_json' in document.metadata:
            import json
            metadata_json = document.metadata['conversion_metadata_json']
            if metadata_json:
                parsed_metadata = json.loads(metadata_json)
                print(f"   Parsed conversion metadata: {parsed_metadata}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ConvertedDocumentService: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("Document Conversion and Save Fixes Test Suite")
    print("=" * 60)
    
    tests = [
        ("Metadata Validation", test_metadata_validation),
        ("ConvertedDocumentService", test_converted_document_service),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("\n🎉 All tests passed! The fixes should resolve the errors.")
        print("\nKey fixes applied:")
        print("• Fixed metadata validation by storing complex data as JSON strings")
        print("• Improved error handling for Knowledge Base imports")
        print("• Added proper integration for new document database features")
        return 0
    else:
        print(f"\n❌ {failed} test(s) failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
