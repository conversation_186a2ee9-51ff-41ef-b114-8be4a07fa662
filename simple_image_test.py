#!/usr/bin/env python3
"""
Simple test for image marker replacement without circular imports.
"""

import sys
import uuid
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import only what we need to avoid circular imports
sys.path.insert(0, str(project_root / "document_manager" / "services"))

from loguru import logger


class ImageInfo:
    """Simple ImageInfo class for testing."""
    
    def __init__(self, data_uri: str, width: int, height: int, position: int, 
                 alt_text: str = "", caption: str = ""):
        self.data_uri = data_uri
        self.width = width
        self.height = height
        self.position = position
        self.alt_text = alt_text
        self.caption = caption
        self.id = str(uuid.uuid4())


def escape_html(text: str) -> str:
    """Escape HTML special characters."""
    if not text:
        return ""
        
    replacements = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;'
    }
    
    for char, escape in replacements.items():
        text = text.replace(char, escape)
        
    return text


def create_html_with_embedded_images(text_content: str, images: list) -> str:
    """Test version of the HTML creation function."""
    try:
        # DEBUGGING: Log the input parameters
        logger.debug(f"Creating HTML with {len(images)} images")
        logger.debug(f"Text content length: {len(text_content)} chars")
        
        # Create a mapping of image IDs to image info
        image_map = {img.id: img for img in images}
        logger.debug(f"Image map keys: {list(image_map.keys())}")
        
        # Count image markers in text
        image_marker_count = text_content.count('[IMAGE:')
        logger.debug(f"Found {image_marker_count} image markers in text")
        
        # Start building HTML
        html_parts = ['<html><body>']
        
        # Process text and replace image markers with actual images
        lines = text_content.split('\n')
        logger.debug(f"Processing {len(lines)} lines of text")
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Check if this line is an image marker
            if line.startswith('[IMAGE:') and line.endswith(']'):
                image_id = line[7:-1]  # Extract ID from [IMAGE:id]
                logger.debug(f"Found image marker on line {line_num}: {line}")
                logger.debug(f"Extracted image ID: {image_id}")
                
                if image_id in image_map:
                    img_info = image_map[image_id]
                    logger.debug(f"Found matching image: {img_info.width}x{img_info.height}")
                    
                    # Create HTML img tag with proper sizing
                    max_width = min(img_info.width, 800)  # Limit max width
                    max_height = min(img_info.height, 600)  # Limit max height
                    
                    img_html = f'''<div style="text-align: center; margin: 10px 0;">
    <img src="{img_info.data_uri}" 
         alt="{img_info.alt_text}" 
         style="max-width: {max_width}px; max-height: {max_height}px; width: auto; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
         title="{img_info.alt_text}" />
    {f'<p style="font-style: italic; color: #666; margin-top: 5px;">{img_info.caption}</p>' if img_info.caption else ''}
</div>'''
                    html_parts.append(img_html)
                    logger.debug(f"Added image HTML for {image_id}")
                else:
                    logger.warning(f"Image ID {image_id} not found in image map")
                    logger.debug(f"Available image IDs: {list(image_map.keys())}")
                    # Add the marker as text if image not found
                    html_parts.append(f'<p>[IMAGE NOT FOUND: {image_id}]</p>')
                continue
            
            # Handle regular text content
            if not line:
                html_parts.append('<br>')
                continue
            
            # Handle headings
            if line.startswith('######'):
                html_parts.append(f'<h6>{escape_html(line[6:].strip())}</h6>')
            elif line.startswith('#####'):
                html_parts.append(f'<h5>{escape_html(line[5:].strip())}</h5>')
            elif line.startswith('####'):
                html_parts.append(f'<h4>{escape_html(line[4:].strip())}</h4>')
            elif line.startswith('###'):
                html_parts.append(f'<h3>{escape_html(line[3:].strip())}</h3>')
            elif line.startswith('##'):
                html_parts.append(f'<h2>{escape_html(line[2:].strip())}</h2>')
            elif line.startswith('#'):
                html_parts.append(f'<h1>{escape_html(line[1:].strip())}</h1>')
            else:
                # Regular paragraph
                escaped_line = escape_html(line)
                escaped_line = escaped_line.replace('\n', '<br>')
                html_parts.append(f'<p>{escaped_line}</p>')
        
        html_parts.append('</body></html>')
        return '\n'.join(html_parts)
        
    except Exception as e:
        logger.error(f"Error creating HTML with embedded images: {e}")
        # Fallback to text without images
        return escape_html(text_content)


def test_image_marker_replacement():
    """Test the image marker replacement logic."""
    print("=== Testing Image Marker Replacement ===")
    
    # Create test content with image markers
    test_content = """# Test Document

This is the first paragraph.

[IMAGE:test-image-1]

This is text after the first image.

## Section 2

Some more content here.

[IMAGE:test-image-2]

Final paragraph."""
    
    # Create mock images with matching IDs
    mock_images = [
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=100,
            height=100,
            position=50,
            alt_text="Test Image 1"
        ),
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=200,
            height=150,
            position=100,
            alt_text="Test Image 2"
        )
    ]
    
    # Set the IDs to match the markers
    mock_images[0].id = "test-image-1"
    mock_images[1].id = "test-image-2"
    
    print("Input text content:")
    print(test_content)
    print(f"\nMock images: {len(mock_images)}")
    for img in mock_images:
        print(f"  - ID: {img.id}, Size: {img.width}x{img.height}")
    
    # Test the HTML generation
    print("\n--- Testing HTML Generation ---")
    html_content = create_html_with_embedded_images(test_content, mock_images)
    
    print(f"Generated HTML ({len(html_content)} chars):")
    print(html_content)
    
    # Check results
    print("\n--- Analysis ---")
    
    # Count markers in input
    input_markers = test_content.count('[IMAGE:')
    print(f"Image markers in input: {input_markers}")
    
    # Count markers remaining in output
    output_markers = html_content.count('[IMAGE:')
    print(f"Image markers remaining in output: {output_markers}")
    
    # Count img tags in output
    img_tags = html_content.count('<img src=')
    print(f"HTML img tags in output: {img_tags}")
    
    # Check for specific markers
    for img in mock_images:
        marker = f"[IMAGE:{img.id}]"
        if marker in html_content:
            print(f"❌ Marker {marker} still present in HTML")
        else:
            print(f"✅ Marker {marker} was replaced")
    
    # Overall result
    if output_markers == 0 and img_tags == len(mock_images):
        print("✅ Image marker replacement working correctly")
        return True
    else:
        print("❌ Image marker replacement has issues")
        return False


def main():
    """Run the simple test."""
    print("Simple Image Marker Test")
    print("=" * 40)
    
    try:
        result = test_image_marker_replacement()
        
        print("\n" + "=" * 40)
        if result:
            print("✅ Test PASSED - Image marker replacement logic is working")
            print("\nThe issue may be:")
            print("1. Images are not being extracted properly")
            print("2. The conversion service is not using the image-aware service")
            print("3. The document conversion dialog is not calling the right method")
        else:
            print("❌ Test FAILED - Image marker replacement logic has issues")
        
        return 0 if result else 1
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logger.error(f"Test error: {e}")
        return 1


if __name__ == "__main__":
    # Configure logging for debugging
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
