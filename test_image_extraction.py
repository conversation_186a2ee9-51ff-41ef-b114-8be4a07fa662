#!/usr/bin/env python3
"""
Test script for image extraction functionality.

This script tests the new image-aware document conversion system to ensure
that images are properly extracted from PDF and DOCX files and embedded
in the HTML output with correct positioning.
"""

import sys
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from document_manager.services.image_aware_document_service import ImageAwareDocumentService
from document_manager.services.document_conversion_service import DocumentConversionService


def test_image_aware_service():
    """Test the ImageAwareDocumentService directly."""
    print("=== Testing ImageAwareDocumentService ===")
    
    service = ImageAwareDocumentService()
    
    # Test with a sample text file (should work without images)
    try:
        # Create a temporary text file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test document.\nIt has multiple lines.\n\nAnd paragraphs.")
            temp_file = Path(f.name)
        
        print(f"Testing with temporary file: {temp_file}")
        
        # Extract content
        text_content, images = service.extract_content_with_images(temp_file)
        
        print(f"Extracted text content ({len(text_content)} chars):")
        print(repr(text_content))
        print(f"\nExtracted {len(images)} images")
        
        # Test HTML creation
        html_content = service.create_html_with_embedded_images(text_content, images)
        print(f"\nGenerated HTML ({len(html_content)} chars):")
        print(html_content[:500] + "..." if len(html_content) > 500 else html_content)
        
        # Clean up
        temp_file.unlink()
        
        print("✅ ImageAwareDocumentService basic test passed")
        
    except Exception as e:
        print(f"❌ ImageAwareDocumentService test failed: {e}")
        logger.error(f"ImageAwareDocumentService test error: {e}")


def test_document_conversion_service():
    """Test the enhanced DocumentConversionService."""
    print("\n=== Testing Enhanced DocumentConversionService ===")
    
    service = DocumentConversionService()
    
    # Test with a sample text file
    try:
        # Create a temporary text file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("# Test Document\n\nThis is a test document with formatting.\n\n## Section 1\n\nSome content here.\n\n## Section 2\n\nMore content.")
            temp_file = Path(f.name)
        
        print(f"Testing conversion with: {temp_file}")
        
        # Convert the document
        doc_format = service.convert_file_to_document_format(
            temp_file,
            document_name="Test Document",
            preserve_formatting=True
        )
        
        if doc_format:
            print("✅ Document conversion successful")
            print(f"Document name: {doc_format.name}")
            print(f"Word count: {doc_format.word_count}")
            print(f"Character count: {doc_format.character_count}")
            print(f"Has images: {doc_format.metadata.get('has_images', False)}")
            print(f"Image count: {doc_format.metadata.get('image_count', 0)}")
            
            # Show a sample of the HTML content
            html_sample = doc_format.html_content[:300] + "..." if len(doc_format.html_content) > 300 else doc_format.html_content
            print(f"HTML content sample:\n{html_sample}")
        else:
            print("❌ Document conversion failed")
        
        # Clean up
        temp_file.unlink()
        
    except Exception as e:
        print(f"❌ DocumentConversionService test failed: {e}")
        logger.error(f"DocumentConversionService test error: {e}")


def test_supported_formats():
    """Test the supported formats functionality."""
    print("\n=== Testing Supported Formats ===")
    
    service = DocumentConversionService()
    
    # Get supported formats
    formats = service.get_supported_formats()
    print("Supported formats:")
    for ext, desc in formats.items():
        print(f"  {ext}: {desc}")
    
    # Test format support checking
    test_files = [
        "test.pdf",
        "test.docx", 
        "test.txt",
        "test.odt",
        "test.xyz"  # Unsupported
    ]
    
    print("\nFormat support check:")
    for file_name in test_files:
        is_supported = service.is_format_supported(file_name)
        status = "✅ Supported" if is_supported else "❌ Not supported"
        print(f"  {file_name}: {status}")


def test_image_html_generation():
    """Test HTML generation with mock images."""
    print("\n=== Testing Image HTML Generation ===")
    
    service = ImageAwareDocumentService()
    
    # Create mock image data
    from document_manager.services.image_aware_document_service import ImageInfo
    
    # Mock text content with image markers
    text_content = """# Test Document with Images

This is the first paragraph.

[IMAGE:img1]

This is text after the first image.

## Section with Image

Some content here.

[IMAGE:img2]

Final paragraph."""
    
    # Create mock images
    mock_images = [
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=100,
            height=100,
            position=50,
            alt_text="Test Image 1",
            caption="This is the first test image"
        ),
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=200,
            height=150,
            position=100,
            alt_text="Test Image 2"
        )
    ]
    
    # Set the IDs to match the markers
    mock_images[0].id = "img1"
    mock_images[1].id = "img2"
    
    print("Mock text content:")
    print(text_content)
    print(f"\nMock images: {len(mock_images)}")
    for i, img in enumerate(mock_images):
        print(f"  Image {i+1}: {img.width}x{img.height}, alt='{img.alt_text}'")
    
    # Generate HTML
    try:
        html_content = service.create_html_with_embedded_images(text_content, mock_images)
        
        print(f"\nGenerated HTML ({len(html_content)} chars):")
        print(html_content)
        
        # Check if images are properly embedded
        if 'data:image/png;base64,' in html_content:
            print("✅ Images properly embedded in HTML")
        else:
            print("❌ Images not found in HTML")
        
        if '<img src=' in html_content:
            img_count = html_content.count('<img src=')
            print(f"✅ Found {img_count} img tags in HTML")
        else:
            print("❌ No img tags found in HTML")
            
    except Exception as e:
        print(f"❌ HTML generation failed: {e}")
        logger.error(f"HTML generation error: {e}")


def main():
    """Run all image extraction tests."""
    print("Image Extraction Test Suite")
    print("=" * 50)
    
    try:
        test_image_aware_service()
        test_document_conversion_service()
        test_supported_formats()
        test_image_html_generation()
        
        print("\n" + "=" * 50)
        print("Test suite completed!")
        print("\nImage extraction functionality is ready.")
        print("\nTo test with real PDF/DOCX files:")
        print("1. Place a PDF or DOCX file with images in the project directory")
        print("2. Use the Document Conversion dialog to convert it")
        print("3. Import into RTF Editor - images should appear with proper positioning")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        logger.error(f"Test suite error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    # Configure logging for testing
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
