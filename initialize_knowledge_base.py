#!/usr/bin/env python3
"""
Knowledge Base Initialization Script

This script initializes and tests the Knowledge Base system to ensure
all components are working correctly.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("Knowledge Base Initialization and Testing")
print("=" * 50)

def test_imports():
    """Test all Knowledge Base imports."""
    print("Testing imports...")
    
    try:
        # Test model imports
        from knowledge_base.models.kb_document import KBDocument, KBDocumentType, KBDocumentStatus
        print("✅ Document models imported")
        
        from knowledge_base.models.kb_collection import KBCollection, KBCollectionType, KBCollectionStatus
        print("✅ Collection models imported")
        
        from knowledge_base.models.kb_section import KBSection
        print("✅ Section models imported")
        
        # Test repository import
        from knowledge_base.repositories import KnowledgeBaseRepository
        print("✅ Repository imported")
        
        # Test service import
        from knowledge_base.services.knowledge_base_service import KnowledgeBaseService
        print("✅ Service imported")
        
        # Test UI import
        from knowledge_base.ui.knowledge_base_panel import KnowledgeBasePanel
        print("✅ UI panel imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_repository():
    """Test repository functionality."""
    print("\nTesting repository...")
    
    try:
        from knowledge_base.repositories import KnowledgeBaseRepository
        
        # Create repository (this will initialize the database)
        repo = KnowledgeBaseRepository("data/test_knowledge_base.db")
        print("✅ Repository created and database initialized")
        
        # Test basic operations
        stats = repo.get_database_stats()
        print(f"✅ Database stats retrieved: {len(stats)} metrics")
        
        return True
        
    except Exception as e:
        print(f"❌ Repository test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service():
    """Test service functionality."""
    print("\nTesting service...")
    
    try:
        from knowledge_base.services.knowledge_base_service import KnowledgeBaseService
        
        # Create service
        service = KnowledgeBaseService("data/test_knowledge_base.db")
        print("✅ Service created")
        
        # Test creating a collection
        collection = service.create_collection(
            name="Test Collection",
            description="A test collection for initialization"
        )
        print(f"✅ Collection created: {collection.name}")
        
        # Test creating a document
        document = service.create_document(
            title="Test Document",
            content="This is a test document for the Knowledge Base system.",
            collection_id=collection.id
        )
        print(f"✅ Document created: {document.title}")
        
        # Test search
        results, total = service.search_documents("test")
        print(f"✅ Search completed: {total} results found")
        
        return True
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui():
    """Test UI components (without showing)."""
    print("\nTesting UI...")
    
    try:
        # Import Qt components
        from PyQt6.QtWidgets import QApplication
        from knowledge_base.ui.knowledge_base_panel import KnowledgeBasePanel
        
        # Create application if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create panel (but don't show it)
        panel = KnowledgeBasePanel()
        print("✅ Knowledge Base panel created")
        
        # Test basic panel properties
        panel.setWindowTitle("Test Knowledge Base")
        print("✅ Panel configured successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_sample_data():
    """Create sample data for testing."""
    print("\nCreating sample data...")
    
    try:
        from knowledge_base.services.knowledge_base_service import KnowledgeBaseService
        
        service = KnowledgeBaseService("data/knowledge_base.db")
        
        # Create sample collections
        general_collection = service.create_collection(
            name="General Knowledge",
            description="General knowledge articles and information"
        )
        
        tech_collection = service.create_collection(
            name="Technical Documentation",
            description="Technical guides and documentation"
        )
        
        # Create sample documents
        welcome_doc = service.create_document(
            title="Welcome to Knowledge Base",
            content="""
            <h1>Welcome to the Knowledge Base System</h1>
            
            <p>This is your comprehensive knowledge management system that allows you to:</p>
            
            <ul>
                <li>Store and organize documents in collections</li>
                <li>Create rich content with formatting, images, and tables</li>
                <li>Search across all your documents</li>
                <li>Track versions and changes</li>
                <li>Add references and annotations</li>
            </ul>
            
            <h2>Getting Started</h2>
            
            <p>To get started with the Knowledge Base:</p>
            
            <ol>
                <li>Create collections to organize your content</li>
                <li>Add documents to your collections</li>
                <li>Use the search feature to find information quickly</li>
                <li>Explore advanced features like versioning and references</li>
            </ol>
            
            <p>The Knowledge Base integrates seamlessly with the document conversion system, 
            allowing you to import existing documents and enhance them with knowledge base features.</p>
            """,
            collection_id=general_collection.id
        )
        
        tech_doc = service.create_document(
            title="System Architecture Overview",
            content="""
            <h1>System Architecture Overview</h1>
            
            <p>The Knowledge Base system is built with a modular architecture:</p>
            
            <h2>Core Components</h2>
            
            <h3>Models</h3>
            <ul>
                <li><strong>KBDocument</strong> - Rich text documents with metadata</li>
                <li><strong>KBCollection</strong> - Hierarchical organization</li>
                <li><strong>KBSection</strong> - Document sections and structure</li>
                <li><strong>KBReference</strong> - Cross-references and citations</li>
                <li><strong>KBAttachment</strong> - File attachments</li>
                <li><strong>KBVersion</strong> - Version control</li>
                <li><strong>KBAnnotation</strong> - Comments and annotations</li>
            </ul>
            
            <h3>Repository Layer</h3>
            <p>The repository provides data persistence with SQLite backend, including:</p>
            <ul>
                <li>Full-text search capabilities</li>
                <li>Relationship management</li>
                <li>Transaction support</li>
                <li>Performance optimization</li>
            </ul>
            
            <h3>Service Layer</h3>
            <p>Business logic and operations including:</p>
            <ul>
                <li>Document lifecycle management</li>
                <li>Collection hierarchy management</li>
                <li>Search and indexing</li>
                <li>Version control</li>
            </ul>
            
            <h3>UI Layer</h3>
            <p>User interface components for:</p>
            <ul>
                <li>Document creation and editing</li>
                <li>Collection management</li>
                <li>Search and navigation</li>
                <li>System administration</li>
            </ul>
            """,
            collection_id=tech_collection.id
        )
        
        print(f"✅ Sample data created:")
        print(f"   - Collections: {general_collection.name}, {tech_collection.name}")
        print(f"   - Documents: {welcome_doc.title}, {tech_doc.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all initialization tests."""
    
    tests = [
        ("Import Tests", test_imports),
        ("Repository Tests", test_repository),
        ("Service Tests", test_service),
        ("UI Tests", test_ui),
        ("Sample Data Creation", create_sample_data),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("INITIALIZATION RESULTS")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("\n🎉 Knowledge Base initialization completed successfully!")
        print("\nThe Knowledge Base system is ready to use:")
        print("• Database initialized with sample data")
        print("• All components tested and working")
        print("• UI components ready for integration")
        print("\nYou can now use the Knowledge Base feature in the application.")
        return 0
    else:
        print(f"\n❌ {failed} test(s) failed.")
        print("The Knowledge Base may need additional setup or debugging.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
