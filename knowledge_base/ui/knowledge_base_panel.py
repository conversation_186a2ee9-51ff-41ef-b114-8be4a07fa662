"""
Knowledge Base Panel

Provides a comprehensive UI for managing the Knowledge Base system,
including document creation, editing, and import functionality.
"""

import time
import psutil
import os
from typing import Optional, List

from loguru import logger
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QTextEdit, QLineEdit, QComboBox, QListWidget, QSplitter,
    QGroupBox, QMessageBox, QProgressBar, QTabWidget
)

from knowledge_base.services.knowledge_base_service import KnowledgeBaseService
from knowledge_base.models.kb_document import KBDocument, KBDocumentType, KBDocumentStatus
from knowledge_base.models.kb_collection import KBCollection, KBCollectionType


class KnowledgeBasePanel(QWidget):
    """Main panel for Knowledge Base management with debugging capabilities."""
    
    def __init__(self, parent=None):
        """Initialize the Knowledge Base panel."""
        super().__init__(parent)
        self.setWindowTitle("Knowledge Base")
        self.setMinimumSize(1000, 700)
        
        # Initialize services
        self.kb_service = KnowledgeBaseService()
        
        # Initialize UI
        self._init_ui()
        
        # Load initial data
        self._refresh_collections()
        
        logger.debug("Knowledge Base panel initialized")
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("📚 Knowledge Base Management")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(title_label)
        
        # Debug info
        self.debug_label = QLabel("Ready")
        self.debug_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        header_layout.addWidget(self.debug_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_documents_tab()
        self._create_import_tab()
        self._create_debug_tab()
    
    def _create_documents_tab(self):
        """Create the documents management tab."""
        documents_widget = QWidget()
        layout = QVBoxLayout(documents_widget)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        new_doc_btn = QPushButton("New Document")
        new_doc_btn.clicked.connect(self._create_new_document)
        toolbar_layout.addWidget(new_doc_btn)
        
        new_collection_btn = QPushButton("New Collection")
        new_collection_btn.clicked.connect(self._create_new_collection)
        toolbar_layout.addWidget(new_collection_btn)
        
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self._refresh_collections)
        toolbar_layout.addWidget(refresh_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # Main content area
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Collections list
        collections_group = QGroupBox("Collections")
        collections_layout = QVBoxLayout(collections_group)
        self.collections_list = QListWidget()
        collections_layout.addWidget(self.collections_list)
        splitter.addWidget(collections_group)
        
        # Documents list
        documents_group = QGroupBox("Documents")
        documents_layout = QVBoxLayout(documents_group)
        self.documents_list = QListWidget()
        documents_layout.addWidget(self.documents_list)
        splitter.addWidget(documents_group)
        
        layout.addWidget(splitter)
        
        self.tab_widget.addTab(documents_widget, "Documents")
    
    def _create_import_tab(self):
        """Create the import/conversion tab with debugging."""
        import_widget = QWidget()
        layout = QVBoxLayout(import_widget)
        
        # Import section
        import_group = QGroupBox("Document Import & Conversion")
        import_layout = QVBoxLayout(import_group)
        
        # Import from RTF Editor button
        import_rtf_btn = QPushButton("Import from RTF Editor")
        import_rtf_btn.setToolTip("Import converted documents from RTF Editor with debugging")
        import_rtf_btn.clicked.connect(self._import_from_rtf_editor)
        import_rtf_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        import_layout.addWidget(import_rtf_btn)
        
        # Progress bar
        self.import_progress = QProgressBar()
        self.import_progress.setVisible(False)
        import_layout.addWidget(self.import_progress)
        
        # Status text
        self.import_status = QLabel("Ready to import documents")
        self.import_status.setStyleSheet("color: #27ae60; font-weight: bold;")
        import_layout.addWidget(self.import_status)
        
        layout.addWidget(import_group)
        
        # Import log
        log_group = QGroupBox("Import Log")
        log_layout = QVBoxLayout(log_group)
        
        self.import_log = QTextEdit()
        self.import_log.setReadOnly(True)
        self.import_log.setMaximumHeight(200)
        log_layout.addWidget(self.import_log)
        
        clear_log_btn = QPushButton("Clear Log")
        clear_log_btn.clicked.connect(self.import_log.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(import_widget, "Import & Debug")
    
    def _create_debug_tab(self):
        """Create the debugging tab."""
        debug_widget = QWidget()
        layout = QVBoxLayout(debug_widget)
        
        # Debug controls
        controls_group = QGroupBox("Debug Controls")
        controls_layout = QHBoxLayout(controls_group)
        
        test_import_btn = QPushButton("Test Import Process")
        test_import_btn.clicked.connect(self._test_import_process)
        controls_layout.addWidget(test_import_btn)
        
        memory_check_btn = QPushButton("Check Memory Usage")
        memory_check_btn.clicked.connect(self._check_memory_usage)
        controls_layout.addWidget(memory_check_btn)
        
        controls_layout.addStretch()
        layout.addWidget(controls_group)
        
        # Debug output
        debug_group = QGroupBox("Debug Output")
        debug_layout = QVBoxLayout(debug_group)
        
        self.debug_output = QTextEdit()
        self.debug_output.setReadOnly(True)
        debug_layout.addWidget(self.debug_output)
        
        clear_debug_btn = QPushButton("Clear Debug Output")
        clear_debug_btn.clicked.connect(self.debug_output.clear)
        debug_layout.addWidget(clear_debug_btn)
        
        layout.addWidget(debug_group)
        
        self.tab_widget.addTab(debug_widget, "Debug")
    
    def _create_new_document(self):
        """Create a new document."""
        try:
            document = self.kb_service.create_document(
                title="New Document",
                content="",
                document_type=KBDocumentType.ARTICLE
            )
            self._log_import(f"Created new document: {document.title}")
            self._refresh_collections()
        except Exception as e:
            logger.error(f"Failed to create new document: {e}")
            self._log_import(f"ERROR: Failed to create new document: {e}")
    
    def _create_new_collection(self):
        """Create a new collection."""
        try:
            collection = self.kb_service.create_collection(
                name="New Collection",
                collection_type=KBCollectionType.FOLDER
            )
            self._log_import(f"Created new collection: {collection.name}")
            self._refresh_collections()
        except Exception as e:
            logger.error(f"Failed to create new collection: {e}")
            self._log_import(f"ERROR: Failed to create new collection: {e}")
    
    def _refresh_collections(self):
        """Refresh the collections and documents lists."""
        try:
            # This would normally load from the knowledge base service
            self.collections_list.clear()
            self.documents_list.clear()

            # Add placeholder items
            self.collections_list.addItem("📁 Default Collection")
            self.documents_list.addItem("📄 Sample Document")

            self._log_import("Refreshed collections and documents")
        except Exception as e:
            logger.error(f"Failed to refresh collections: {e}")
            self._log_import(f"ERROR: Failed to refresh collections: {e}")

    def _import_from_rtf_editor(self):
        """Import documents from RTF Editor with detailed debugging."""
        start_time = time.time()
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        self._log_import("=== KNOWLEDGE BASE IMPORT DEBUG START ===")
        self._log_import(f"Import started at: {time.strftime('%H:%M:%S')}")
        self._log_import(f"Initial memory usage: {initial_memory:.2f} MB")

        try:
            # Show progress
            self.import_progress.setVisible(True)
            self.import_progress.setRange(0, 0)  # Indeterminate progress
            self.import_status.setText("Importing documents...")
            self.import_status.setStyleSheet("color: #f39c12; font-weight: bold;")

            # Simulate document conversion process (this would normally come from RTF Editor)
            self._simulate_document_conversion()

            # Complete
            total_time = time.time() - start_time
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_delta = final_memory - initial_memory

            self.import_progress.setVisible(False)
            self.import_status.setText("Import completed successfully")
            self.import_status.setStyleSheet("color: #27ae60; font-weight: bold;")

            self._log_import(f"=== KNOWLEDGE BASE IMPORT DEBUG COMPLETE ===")
            self._log_import(f"Total import time: {total_time:.3f} seconds")
            self._log_import(f"Final memory usage: {final_memory:.2f} MB")
            self._log_import(f"Memory delta: {memory_delta:+.2f} MB")

        except Exception as e:
            error_time = time.time()
            self.import_progress.setVisible(False)
            self.import_status.setText("Import failed")
            self.import_status.setStyleSheet("color: #e74c3c; font-weight: bold;")

            self._log_import(f"IMPORT ERROR at {error_time}: {e}")
            self._log_import(f"Error occurred after {error_time - start_time:.3f} seconds")

            QMessageBox.critical(
                self,
                "Import Error",
                f"Failed to import documents:\n{str(e)}\n\n"
                f"Check the import log for detailed information."
            )

    def _simulate_document_conversion(self):
        """Simulate the document conversion process for testing."""
        self._log_import("Simulating document conversion process...")

        # Simulate creating a large document
        large_content = "This is a test document. " * 10000  # ~250KB of text
        self._log_import(f"Created test content: {len(large_content):,} characters")

        # Simulate HTML content
        html_content = f"<html><body><p>{'Test paragraph. ' * 5000}</p></body></html>"
        self._log_import(f"Created test HTML: {len(html_content):,} characters")

        # Simulate the RTF Editor loading process
        self._log_import("Simulating RTF Editor loading...")

        # Create a mock document format
        class MockDocumentFormat:
            def __init__(self):
                self.name = "Test Document"
                self.html_content = html_content
                self.content = large_content
                self.id = "test_doc_001"

        mock_doc = MockDocumentFormat()

        # Simulate the loading process that might cause freezing
        self._test_document_loading(mock_doc)

    def _test_document_loading(self, document_format):
        """Test the document loading process."""
        load_start = time.time()
        self._log_import(f"Testing document loading for: {document_format.name}")

        try:
            # Simulate content size checks
            if hasattr(document_format, 'html_content'):
                html_size = len(document_format.html_content)
                self._log_import(f"HTML content size: {html_size:,} characters ({html_size/1024:.2f} KB)")

                if html_size > 200000:  # 200KB limit
                    self._log_import("WARNING: HTML content exceeds size limit")

            if hasattr(document_format, 'content'):
                content_size = len(document_format.content)
                self._log_import(f"Plain content size: {content_size:,} characters ({content_size/1024:.2f} KB)")

                if content_size > 500000:  # 500KB limit
                    self._log_import("WARNING: Plain content exceeds size limit")

            # Simulate processing time
            QTimer.singleShot(100, lambda: self._log_import("Document processing completed"))

            load_time = time.time() - load_start
            self._log_import(f"Document loading test completed in: {load_time:.3f} seconds")

        except Exception as e:
            self._log_import(f"ERROR in document loading test: {e}")

    def _test_import_process(self):
        """Test the import process for debugging."""
        self._log_debug("=== TESTING IMPORT PROCESS ===")

        try:
            # Test memory usage
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            self._log_debug(f"Memory before test: {memory_before:.2f} MB")

            # Simulate heavy processing
            test_data = "x" * 1000000  # 1MB of data
            self._log_debug(f"Created test data: {len(test_data):,} bytes")

            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_delta = memory_after - memory_before
            self._log_debug(f"Memory after test: {memory_after:.2f} MB (delta: {memory_delta:+.2f} MB)")

            # Clean up
            del test_data

            self._log_debug("Import process test completed successfully")

        except Exception as e:
            self._log_debug(f"ERROR in import process test: {e}")

    def _check_memory_usage(self):
        """Check current memory usage."""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()

            rss_mb = memory_info.rss / 1024 / 1024  # MB
            vms_mb = memory_info.vms / 1024 / 1024  # MB

            self._log_debug(f"=== MEMORY USAGE CHECK ===")
            self._log_debug(f"RSS (Resident Set Size): {rss_mb:.2f} MB")
            self._log_debug(f"VMS (Virtual Memory Size): {vms_mb:.2f} MB")
            self._log_debug(f"CPU percent: {process.cpu_percent():.1f}%")

            # Update debug label
            self.debug_label.setText(f"Memory: {rss_mb:.1f} MB")

        except Exception as e:
            self._log_debug(f"ERROR checking memory usage: {e}")

    def _log_import(self, message: str):
        """Log a message to the import log."""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.import_log.append(formatted_message)
        logger.debug(f"KB Import: {message}")

    def _log_debug(self, message: str):
        """Log a message to the debug output."""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.debug_output.append(formatted_message)
        logger.debug(f"KB Debug: {message}")
