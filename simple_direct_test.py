#!/usr/bin/env python3
"""
Simple test for direct image embedding.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("Testing direct image embedding approach...")

try:
    # Test import
    from document_manager.services.image_aware_document_service import ImageAwareDocumentService
    print("✅ Successfully imported ImageAwareDocumentService")
    
    # Create service
    service = ImageAwareDocumentService()
    print("✅ Successfully created service instance")
    
    # Test with simple text content that has embedded HTML
    test_content = '''# Test Document

This is a paragraph.

<div style="text-align: center; margin: 10px 0;">
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" 
         alt="Test Image" 
         style="max-width: 100px; max-height: 100px; width: auto; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
         title="Test Image" />
</div>

This is after the image.'''
    
    print(f"Test content ({len(test_content)} chars):")
    print(test_content[:200] + "..." if len(test_content) > 200 else test_content)
    
    # Test HTML generation
    html_result = service.create_html_with_embedded_images(test_content, [])
    
    print(f"\nGenerated HTML ({len(html_result)} chars):")
    print(html_result[:300] + "..." if len(html_result) > 300 else html_result)
    
    # Check results
    has_img_tags = '<img src=' in html_result
    has_data_uri = 'data:image/' in html_result
    has_uuid_markers = '[IMAGE:' in html_result
    
    print(f"\nAnalysis:")
    print(f"Has img tags: {has_img_tags}")
    print(f"Has data URIs: {has_data_uri}")
    print(f"Has UUID markers: {has_uuid_markers}")
    
    if has_img_tags and has_data_uri and not has_uuid_markers:
        print("✅ Direct embedding approach is working!")
    else:
        print("❌ Direct embedding approach has issues")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\nDirect embedding test completed.")
