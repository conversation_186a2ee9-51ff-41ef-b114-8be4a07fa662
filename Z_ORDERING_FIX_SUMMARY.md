# Z-Ordering Fix for Knowledge Base Import Dialog

## Problem Identified

The issue you experienced was **not** with the Knowledge Base import process itself, but with the **z-ordering of the success dialog**. Here's what was happening:

1. ✅ **Document import worked perfectly** - logs show successful completion in 0.033 seconds
2. ✅ **RTF Editor window created successfully** - window opened with proper content
3. ❌ **Success dialog hidden behind RTF Editor** - dialog appeared but was not visible
4. 🔒 **Application appeared frozen** - modal dialog waiting for user input but hidden

## Root Cause

The RTF Editor windows are created with `WindowStaysOnTopHint` flag, which makes them appear above all other windows. When the success dialog was shown using the standard `QMessageBox.information()` method, it appeared **behind** the RTF Editor window, making the application seem frozen while waiting for user interaction with an invisible dialog.

## Solution Implemented

### 1. Fixed Success Dialog (`document_manager/ui/dialogs/document_conversion_dialog.py`)

**Before (problematic):**
```python
QMessageBox.information(
    self,
    "Import Successful",
    f"Successfully imported {len(self.converted_documents)} documents to RTF editor."
)
```

**After (fixed):**
```python
# Create message box with proper z-ordering
msg_box = QMessageBox(self)
msg_box.setWindowTitle("Import Successful")
msg_box.setText(f"Successfully imported {len(self.converted_documents)} documents to RTF editor.")
msg_box.setIcon(QMessageBox.Icon.Information)
msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

# Ensure the dialog stays on top of RTF Editor windows
msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

# Show the dialog
msg_box.exec()
```

### 2. Added Timing Delay

Added a 100ms delay before showing the success dialog to ensure RTF Editor windows are fully created:

```python
# Add a small delay to ensure RTF Editor windows are fully created
QTimer.singleShot(100, show_success_dialog)
```

### 3. Fixed Other Potential Z-Ordering Issues

Also fixed similar issues in `document_manager/ui/document_tab.py`:

- **Error dialogs** during import failures
- **Large document warning dialogs** 

All now use the same pattern with `WindowStaysOnTopHint` to ensure visibility.

## Files Modified

1. **`document_manager/ui/dialogs/document_conversion_dialog.py`**
   - Fixed success dialog z-ordering
   - Added timing delay for proper sequencing

2. **`document_manager/ui/document_tab.py`**
   - Fixed error dialog z-ordering
   - Fixed large document warning dialog z-ordering

3. **`requirements.txt`**
   - Added `psutil>=5.9.0` for debugging capabilities

## Testing

### Manual Testing
1. Import documents through Document Conversion dialog
2. Success dialog should now appear on top of RTF Editor windows
3. No more "frozen" application behavior

### Automated Testing
Run the test script to verify z-ordering behavior:
```bash
python test_z_ordering_fix.py
```

This test creates a window with `WindowStaysOnTopHint` and demonstrates the difference between old and new dialog methods.

## Key Technical Details

### WindowStaysOnTopHint Behavior
- RTF Editor windows use `Qt.WindowType.WindowStaysOnTopHint`
- This flag makes windows appear above all other windows
- Standard dialogs without this flag appear behind stay-on-top windows
- Solution: Add the same flag to dialogs that need to appear above RTF Editor windows

### Dialog Creation Pattern
```python
# Create custom message box
msg_box = QMessageBox(parent)
msg_box.setWindowTitle("Title")
msg_box.setText("Message")
msg_box.setIcon(QMessageBox.Icon.Information)
msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

# Critical: Add WindowStaysOnTopHint
msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

# Show dialog
msg_box.exec()
```

## Prevention

To prevent similar issues in the future:

1. **Always use custom QMessageBox creation** when showing dialogs that need to appear above RTF Editor windows
2. **Add WindowStaysOnTopHint** to any dialog that might appear during or after RTF Editor operations
3. **Test dialog visibility** when RTF Editor windows are open
4. **Consider timing** - use QTimer.singleShot() for dialogs that appear immediately after window creation

## Verification

The fix ensures that:
- ✅ Success dialogs appear on top of RTF Editor windows
- ✅ Error dialogs are visible when import fails
- ✅ Warning dialogs for large documents are visible
- ✅ No more "frozen" application behavior
- ✅ User can interact with all dialogs normally

The debugging system we implemented earlier helped identify that the import process was working correctly, leading us to discover the real issue was with dialog visibility rather than import functionality.
