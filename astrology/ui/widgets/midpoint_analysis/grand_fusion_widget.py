"""
Purpose: Widget for displaying the Grand Fusion Midpoint.

This file provides a visualization of the Grand Fusion Midpoint,
a unique point that both derives from and can generate all 21 traditional midpoints.

Key components:
- GrandFusionWidget: Widget for displaying the Grand Fusion Midpoint

Dependencies:
- PyQt6: For UI components
- loguru: For logging
- astrology.models: For astrological data models
- astrology.services: For astrological services
"""

import math
from typing import Optional

from PyQt6.QtCore import QPoint, Qt
from PyQt6.QtGui import (
    QBrush,
    QColor,
    QFont,
    QMouseEvent,
    QPainter,
    QPen,
    QRadialGradient,
)
from PyQt6.QtWidgets import (
    QFrame,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QToolTip,
    QVBoxLayout,
    QWidget,
)

from astrology.models.chart import Chart
from astrology.services.grand_fusion_service import GrandFusionService


class GrandFusionWidget(QWidget):
    """Widget for displaying the Grand Fusion Midpoint."""

    def __init__(self, chart: Optional[Chart] = None):
        """Initialize the Grand Fusion widget.

        Args:
            chart: The chart to display
        """
        super().__init__()
        self.chart = chart
        self.fusion_service = GrandFusionService()
        self.fusion_point = None
        self.traditional_planets = [
            "sun",
            "moon",
            "mercury",
            "venus",
            "mars",
            "jupiter",
            "saturn",
        ]
        self.planet_colors = {
            "sun": QColor("#FFD700"),  # Gold
            "moon": QColor("#C0C0C0"),  # Silver
            "mercury": QColor("#708090"),  # Slate Gray
            "venus": QColor("#00FF7F"),  # Spring Green
            "mars": QColor("#FF4500"),  # Orange Red
            "jupiter": QColor("#4169E1"),  # Royal Blue
            "saturn": QColor("#708090"),  # Slate Gray
        }

        # Store midpoint positions and tooltips for hover functionality
        self.midpoint_positions = {}
        self.fusion_position = None

        self._init_ui()

        if chart:
            self.set_chart(chart)

    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)

        # Add title with Hebrew
        title_layout = QHBoxLayout()

        # Hebrew title
        hebrew_title = QLabel("מרכבת היחוד")
        hebrew_title.setStyleSheet(
            "font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #8B4513;"
        )
        hebrew_title.setAlignment(Qt.AlignmentFlag.AlignRight)
        title_layout.addWidget(hebrew_title)

        # Main title
        title = QLabel("Grand Fusion Midpoint")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title)

        # Add the title layout
        layout.addLayout(title_layout)

        # Add subtitle
        subtitle = QLabel("Merkavat HaYichud - The Chariot of Unity")
        subtitle.setStyleSheet(
            "font-size: 14px; font-style: italic; margin-bottom: 5px;"
        )
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle)

        # Add Tarot correspondence
        tarot = QLabel("Tarot Correspondence: The Chariot (VII)")
        tarot.setStyleSheet("font-size: 12px; color: #4B0082; margin-bottom: 10px;")
        tarot.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(tarot)

        # Add explanation
        explanation = QLabel(
            "The Grand Fusion Midpoint represents the mathematical essence that both generates "
            "and is generated by all planetary relationships in your chart. It reveals the underlying "
            "pattern that connects all midpoints in a coherent system, indicating the fundamental "
            "organizing principle of your psyche. As the 22nd midpoint, it embodies 'The Grand Synthesis' - "
            "the integrative and transcendental point from which all other midpoints derive."
        )
        explanation.setWordWrap(True)
        explanation.setStyleSheet(
            "font-style: italic; color: #666; margin: 10px 0; padding: 5px;"
        )
        explanation.setMinimumHeight(80)  # Ensure enough height for wrapped text
        layout.addWidget(explanation)

        # Create splitter for visualization and table
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # Add visualization frame
        self.visual_frame = QFrame()
        self.visual_frame.setMinimumHeight(500)  # Increased from 250 to 500
        self.visual_frame.setMinimumWidth(
            500
        )  # Added minimum width to ensure circular display
        self.visual_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.visual_frame.setFrameShadow(QFrame.Shadow.Sunken)
        self.visual_frame.setMouseTracking(True)  # Enable mouse tracking for tooltips
        self.visual_frame.paintEvent = self._paint_visualization
        self.visual_frame.mouseMoveEvent = self._handle_mouse_move
        splitter.addWidget(self.visual_frame)

        # Add table for midpoint derivations
        table_container = QWidget()
        table_layout = QVBoxLayout(table_container)

        table_title = QLabel("The 21 Midpoints Derived from the Chariot of Unity")
        table_title.setStyleSheet("font-weight: bold; color: #4B0082;")
        table_layout.addWidget(table_title)

        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(
            ["Midpoint", "Original", "Derived", "Difference"]
        )

        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        # Enable word wrap
        self.table.setWordWrap(True)
        self.table.setTextElideMode(Qt.TextElideMode.ElideNone)

        table_layout.addWidget(self.table)
        splitter.addWidget(table_container)

        # Set splitter proportions
        splitter.setSizes([250, 350])

    def set_chart(self, chart: Chart):
        """Set the chart and update the display.

        Args:
            chart: The chart to display
        """
        self.chart = chart
        self._update_display()

    def _update_display(self):
        """Update the display with the current chart data."""
        if not self.chart:
            return

        # Calculate the Grand Fusion Midpoint
        self.fusion_point = self.fusion_service.calculate_grand_fusion_midpoint(
            self.chart
        )
        if self.fusion_point is None:
            return

        # Update the visualization
        self.visual_frame.update()

        # Update the table
        self._update_table()

    def _update_table(self):
        """Update the table with derived midpoints."""
        if not self.chart or self.fusion_point is None:
            self.table.setRowCount(0)
            return

        # Get all original midpoints
        midpoints, planet_pairs = self.fusion_service._calculate_all_midpoints(
            self.chart.kerykeion_subject
        )

        # Set the number of rows
        self.table.setRowCount(len(midpoints))

        # Fill the table
        for i, ((p1, p2), original_pos) in enumerate(
            zip(planet_pairs, midpoints.values())
        ):
            # Midpoint name
            midpoint_name = f"{p1.capitalize()}/{p2.capitalize()}"
            name_item = QTableWidgetItem(midpoint_name)
            name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(i, 0, name_item)

            # Original position
            original_str = f"{original_pos:.2f}° {self.fusion_service.get_sign_for_position(original_pos)}"
            original_item = QTableWidgetItem(original_str)
            original_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(i, 1, original_item)

            # Derived position
            derived_pos = self.fusion_service.reconstruct_midpoint(
                self.fusion_point, p1, p2
            )
            derived_str = f"{derived_pos:.2f}° {self.fusion_service.get_sign_for_position(derived_pos)}"
            derived_item = QTableWidgetItem(derived_str)
            derived_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(i, 2, derived_item)

            # Difference
            diff = abs(original_pos - derived_pos)
            if diff > 180:
                diff = 360 - diff
            diff_str = f"{diff:.2f}°"

            # Color code the difference
            diff_item = QTableWidgetItem(diff_str)
            diff_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # Set color based on accuracy
            if diff < 1:
                diff_item.setForeground(QColor("#009900"))  # Green for very accurate
            elif diff < 3:
                diff_item.setForeground(QColor("#999900"))  # Yellow-green for good
            elif diff < 5:
                diff_item.setForeground(QColor("#996600"))  # Orange for moderate
            else:
                diff_item.setForeground(
                    QColor("#990000")
                )  # Red for significant difference

            self.table.setItem(i, 3, diff_item)

        # Resize rows to fit content
        self.table.resizeRowsToContents()

    def _paint_visualization(self, event):
        """Paint the Grand Fusion visualization.

        Args:
            event: Paint event
        """
        if not self.chart or self.fusion_point is None:
            return

        painter = QPainter(self.visual_frame)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Get frame dimensions
        width = self.visual_frame.width()
        height = self.visual_frame.height()

        # Calculate center and radius
        center_x = width / 2
        center_y = height / 2
        radius = min(width, height) / 2 - 40

        # Draw background gradient
        gradient = QRadialGradient(center_x, center_y, radius)
        gradient.setColorAt(0, QColor(30, 30, 50))
        gradient.setColorAt(1, QColor(10, 10, 30))
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(
            int(center_x - radius),
            int(center_y - radius),
            int(radius * 2),
            int(radius * 2),
        )

        # Draw zodiac ring
        self._draw_zodiac_ring(painter, center_x, center_y, radius)

        # Draw midpoints
        self._draw_midpoints(painter, center_x, center_y, radius)

        # Draw Grand Fusion Midpoint
        self._draw_fusion_point(painter, center_x, center_y, radius)

        # Draw connection lines
        self._draw_connection_lines(painter, center_x, center_y, radius)

        painter.end()

    def _draw_zodiac_ring(self, painter, center_x, center_y, radius):
        """Draw the zodiac ring.

        Args:
            painter: QPainter object
            center_x: X coordinate of the center
            center_y: Y coordinate of the center
            radius: Radius of the chart
        """
        # Draw outer circle - increased line width
        painter.setPen(QPen(QColor("#FFFFFF"), 3))  # Increased from 2 to 3
        painter.drawEllipse(
            int(center_x - radius),
            int(center_y - radius),
            int(radius * 2),
            int(radius * 2),
        )

        # Draw inner circle - increased line width
        inner_radius = radius * 0.9
        painter.setPen(QPen(QColor("#AAAAAA"), 2))  # Increased from 1 to 2
        painter.drawEllipse(
            int(center_x - inner_radius),
            int(center_y - inner_radius),
            int(inner_radius * 2),
            int(inner_radius * 2),
        )

        # Draw zodiac signs
        signs = ["♈", "♉", "♊", "♋", "♌", "♍", "♎", "♏", "♐", "♑", "♒", "♓"]

        for i, sign in enumerate(signs):
            angle = i * 30
            angle_rad = math.radians(angle)

            # Calculate position
            x = center_x + (radius * 0.95) * math.cos(angle_rad - math.pi / 2)
            y = center_y + (radius * 0.95) * math.sin(angle_rad - math.pi / 2)

            # Draw sign symbol - increased size
            painter.setPen(QPen(QColor("#FFFFFF"), 2))  # Increased from 1 to 2
            painter.setFont(QFont("Arial", 16))  # Increased from 12 to 16
            painter.drawText(int(x - 8), int(y + 8), sign)

    def _draw_midpoints(self, painter, center_x, center_y, radius):
        """Draw the midpoints.

        Args:
            painter: QPainter object
            center_x: X coordinate of the center
            center_y: Y coordinate of the center
            radius: Radius of the chart
        """
        if not self.chart:
            return

        # Clear previous midpoint positions
        self.midpoint_positions = {}

        # Get all midpoints
        midpoints, planet_pairs = self.fusion_service._calculate_all_midpoints(
            self.chart.kerykeion_subject
        )

        # Draw each midpoint
        for (p1, p2), position in zip(planet_pairs, midpoints.values()):
            # Calculate position on the circle
            angle_rad = math.radians(position - 90)  # Subtract 90 to start at the top
            x = center_x + radius * 0.8 * math.cos(angle_rad)
            y = center_y + radius * 0.8 * math.sin(angle_rad)

            # Determine color based on planets involved
            # Use a blend of the two planet colors
            color1 = self.planet_colors.get(p1, QColor("#FFFFFF"))
            color2 = self.planet_colors.get(p2, QColor("#FFFFFF"))
            blended_color = QColor(
                (color1.red() + color2.red()) // 2,
                (color1.green() + color2.green()) // 2,
                (color1.blue() + color2.blue()) // 2,
            )

            # Draw midpoint marker with planet-specific color
            painter.setPen(QPen(QColor("#000000"), 1))
            painter.setBrush(QBrush(blended_color))
            painter.drawEllipse(int(x - 5), int(y - 5), 10, 10)  # Size: 10x10

            # Store midpoint position and info for tooltip
            midpoint_key = f"{p1}_{p2}"
            self.midpoint_positions[midpoint_key] = {
                "x": x,
                "y": y,
                "radius": 5,
                "label": f"{p1.capitalize()}/{p2.capitalize()}",
                "position": f"{position:.2f}° {self.fusion_service.get_sign_for_position(position)}",
                "color": blended_color,
            }

    def _draw_fusion_point(self, painter, center_x, center_y, radius):
        """Draw the Grand Fusion Midpoint.

        Args:
            painter: QPainter object
            center_x: X coordinate of the center
            center_y: Y coordinate of the center
            radius: Radius of the chart
        """
        if self.fusion_point is None:
            return

        # Calculate position on the circle
        angle_rad = math.radians(
            self.fusion_point - 90
        )  # Subtract 90 to start at the top
        x = center_x + radius * 0.5 * math.cos(angle_rad)
        y = center_y + radius * 0.5 * math.sin(angle_rad)

        # Store fusion point position for tooltip
        self.fusion_position = {
            "x": x,
            "y": y,
            "radius": 15,  # Larger radius for easier hovering
            "label": "Grand Fusion Midpoint - Merkavat HaYichud",
            "position": f"{self.fusion_point:.2f}° {self.fusion_service.get_sign_for_position(self.fusion_point)}",
            "description": "The Chariot of Unity - The integrative and transcendental midpoint from which all midpoints derive.",
        }

        # Draw fusion point marker with chariot symbolism and glow effect
        # Outer glow - representing the Chariot's aura - increased size
        for i in range(15, 0, -1):  # Increased range from 10 to 15
            alpha = 100 - i * 6  # Adjusted to maintain proper alpha gradient
            color = QColor(255, 215, 0, alpha)  # Gold with decreasing alpha
            painter.setPen(QPen(color, 2))  # Increased from 1 to 2
            painter.setBrush(QBrush(color))
            size = 30 - i  # Increased from 20 to 30
            painter.drawEllipse(int(x - size / 2), int(y - size / 2), size, size)

        # Inner point - the Chariot itself - increased size
        painter.setPen(QPen(QColor("#FFFFFF"), 3))  # Increased from 2 to 3
        painter.setBrush(QBrush(QColor(255, 215, 0)))  # Gold
        painter.drawEllipse(
            int(x - 12), int(y - 12), 24, 24
        )  # Increased from 16x16 to 24x24

        # Draw chariot symbol (simplified) - increased size
        painter.setPen(QPen(QColor("#000000"), 2))  # Increased from 1 to 2
        # Wheels
        painter.drawEllipse(int(x - 7), int(y + 3), 6, 6)  # Increased from 4x4 to 6x6
        painter.drawEllipse(int(x + 1), int(y + 3), 6, 6)  # Increased from 4x4 to 6x6
        # Chariot body
        painter.drawLine(
            int(x - 9), int(y - 3), int(x + 9), int(y - 3)
        )  # Increased width
        painter.drawLine(
            int(x - 9), int(y - 3), int(x - 9), int(y + 3)
        )  # Increased height
        painter.drawLine(
            int(x + 9), int(y - 3), int(x + 9), int(y + 3)
        )  # Increased height

        # Draw position text - increased size
        sign = self.fusion_service.get_sign_for_position(self.fusion_point)
        position_text = f"{self.fusion_point:.2f}° {sign}"
        painter.setFont(
            QFont("Arial", 16, QFont.Weight.Bold)
        )  # Increased from 12 to 16
        painter.drawText(
            int(center_x - 80), int(center_y), position_text
        )  # Adjusted position

    def _draw_connection_lines(self, painter, center_x, center_y, radius):
        """Draw lines connecting the fusion point to midpoints.

        Args:
            painter: QPainter object
            center_x: X coordinate of the center
            center_y: Y coordinate of the center
            radius: Radius of the chart
        """
        if self.fusion_point is None or self.fusion_position is None:
            return

        # Get all midpoints
        midpoints, planet_pairs = self.fusion_service._calculate_all_midpoints(
            self.chart.kerykeion_subject
        )

        # Get fusion point position
        fusion_x = self.fusion_position["x"]
        fusion_y = self.fusion_position["y"]

        # Draw connection lines to each midpoint
        for midpoint_key, midpoint_info in self.midpoint_positions.items():
            # Get midpoint position
            x = midpoint_info["x"]
            y = midpoint_info["y"]

            # Draw connection line with color matching the midpoint
            color = midpoint_info["color"]
            pen = QPen(
                QColor(color.red(), color.green(), color.blue(), 100),
                1,
                Qt.PenStyle.DashLine,
            )  # Semi-transparent
            painter.setPen(pen)
            painter.drawLine(int(fusion_x), int(fusion_y), int(x), int(y))

    def _handle_mouse_move(self, event: QMouseEvent):
        """Handle mouse movement for tooltips.

        Args:
            event: Mouse event
        """
        # Get mouse position
        mouse_x = event.position().x()
        mouse_y = event.position().y()

        # Check if mouse is over the fusion point
        if self.fusion_position:
            x = self.fusion_position["x"]
            y = self.fusion_position["y"]
            radius = self.fusion_position["radius"]

            # Calculate distance from mouse to fusion point
            distance = math.sqrt((mouse_x - x) ** 2 + (mouse_y - y) ** 2)

            if distance <= radius:
                # Show tooltip for fusion point
                tooltip_text = f"<b>{self.fusion_position['label']}</b><br>"
                tooltip_text += f"Position: {self.fusion_position['position']}<br>"
                tooltip_text += f"<i>{self.fusion_position['description']}</i>"
                QToolTip.showText(
                    self.visual_frame.mapToGlobal(QPoint(int(mouse_x), int(mouse_y))),
                    tooltip_text,
                )
                return

        # Check if mouse is over any midpoint
        for midpoint_key, midpoint_info in self.midpoint_positions.items():
            x = midpoint_info["x"]
            y = midpoint_info["y"]
            radius = midpoint_info["radius"]

            # Calculate distance from mouse to midpoint
            distance = math.sqrt((mouse_x - x) ** 2 + (mouse_y - y) ** 2)

            if distance <= radius:
                # Show tooltip for midpoint
                tooltip_text = f"<b>{midpoint_info['label']}</b><br>"
                tooltip_text += f"Position: {midpoint_info['position']}"
                QToolTip.showText(
                    self.visual_frame.mapToGlobal(QPoint(int(mouse_x), int(mouse_y))),
                    tooltip_text,
                )
                return

        # If not over any point, hide tooltip
        QToolTip.hideText()
