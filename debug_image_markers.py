#!/usr/bin/env python3
"""
Debug script to test image marker replacement.

This script tests the image marker replacement logic to identify why
[IMAGE:id] markers are appearing as literal text instead of being
replaced with actual images.
"""

import sys
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from document_manager.services.image_aware_document_service import ImageAwareDocumentService, ImageInfo


def test_image_marker_replacement():
    """Test the image marker replacement logic directly."""
    print("=== Testing Image Marker Replacement ===")
    
    service = ImageAwareDocumentService()
    
    # Create test content with image markers
    test_content = """# Test Document

This is the first paragraph.

[IMAGE:test-image-1]

This is text after the first image.

## Section 2

Some more content here.

[IMAGE:test-image-2]

Final paragraph."""
    
    # Create mock images with matching IDs
    mock_images = [
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=100,
            height=100,
            position=50,
            alt_text="Test Image 1"
        ),
        ImageInfo(
            data_uri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            width=200,
            height=150,
            position=100,
            alt_text="Test Image 2"
        )
    ]
    
    # Set the IDs to match the markers
    mock_images[0].id = "test-image-1"
    mock_images[1].id = "test-image-2"
    
    print("Input text content:")
    print(test_content)
    print(f"\nMock images: {len(mock_images)}")
    for img in mock_images:
        print(f"  - ID: {img.id}, Size: {img.width}x{img.height}")
    
    # Test the HTML generation
    print("\n--- Testing HTML Generation ---")
    html_content = service.create_html_with_embedded_images(test_content, mock_images)
    
    print(f"Generated HTML ({len(html_content)} chars):")
    print(html_content)
    
    # Check results
    print("\n--- Analysis ---")
    
    # Count markers in input
    input_markers = test_content.count('[IMAGE:')
    print(f"Image markers in input: {input_markers}")
    
    # Count markers remaining in output
    output_markers = html_content.count('[IMAGE:')
    print(f"Image markers remaining in output: {output_markers}")
    
    # Count img tags in output
    img_tags = html_content.count('<img src=')
    print(f"HTML img tags in output: {img_tags}")
    
    # Check for specific markers
    for img in mock_images:
        marker = f"[IMAGE:{img.id}]"
        if marker in html_content:
            print(f"❌ Marker {marker} still present in HTML")
        else:
            print(f"✅ Marker {marker} was replaced")
    
    # Overall result
    if output_markers == 0 and img_tags == len(mock_images):
        print("✅ Image marker replacement working correctly")
        return True
    else:
        print("❌ Image marker replacement has issues")
        return False


def test_real_docx_conversion():
    """Test with a real DOCX file if available."""
    print("\n=== Testing Real DOCX Conversion ===")
    
    # Look for DOCX files in the current directory
    docx_files = list(Path('.').glob('*.docx'))
    
    if not docx_files:
        print("No DOCX files found in current directory")
        print("To test with a real file:")
        print("1. Place a DOCX file with images in this directory")
        print("2. Run this script again")
        return True
    
    # Use the first DOCX file found
    docx_file = docx_files[0]
    print(f"Testing with: {docx_file}")
    
    try:
        from document_manager.services.document_conversion_service import DocumentConversionService
        
        service = DocumentConversionService()
        
        # Convert with image extraction enabled
        doc_format = service.convert_file_to_document_format(
            docx_file,
            preserve_formatting=True
        )
        
        if doc_format:
            print("✅ Conversion successful")
            print(f"Has images: {doc_format.metadata.get('has_images', False)}")
            print(f"Image count: {doc_format.metadata.get('image_count', 0)}")
            
            # Check for image markers in HTML
            html_content = doc_format.html_content
            image_markers = html_content.count('[IMAGE:')
            img_tags = html_content.count('<img src=')
            
            print(f"Image markers in HTML: {image_markers}")
            print(f"IMG tags in HTML: {img_tags}")
            
            if image_markers > 0:
                print("❌ Image markers found in HTML - replacement failed")
                # Show a sample of the HTML around the markers
                lines = html_content.split('\n')
                for i, line in enumerate(lines):
                    if '[IMAGE:' in line:
                        print(f"Line {i}: {line}")
                        break
            else:
                print("✅ No image markers in HTML - replacement successful")
            
            return image_markers == 0
        else:
            print("❌ Conversion failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        logger.error(f"Conversion error: {e}")
        return False


def test_image_extraction_only():
    """Test just the image extraction part."""
    print("\n=== Testing Image Extraction Only ===")
    
    # Look for DOCX files
    docx_files = list(Path('.').glob('*.docx'))
    
    if not docx_files:
        print("No DOCX files found for extraction test")
        return True
    
    docx_file = docx_files[0]
    print(f"Testing extraction with: {docx_file}")
    
    try:
        service = ImageAwareDocumentService()
        text_content, images = service.extract_content_with_images(docx_file)
        
        print(f"Extracted text ({len(text_content)} chars):")
        print(text_content[:300] + "..." if len(text_content) > 300 else text_content)
        
        print(f"\nExtracted {len(images)} images:")
        for i, img in enumerate(images):
            print(f"  Image {i+1}: ID={img.id}, Size={img.width}x{img.height}")
            print(f"    Data URI length: {len(img.data_uri)} chars")
        
        # Check for image markers in text
        image_markers = text_content.count('[IMAGE:')
        print(f"\nImage markers in extracted text: {image_markers}")
        
        if len(images) > 0 and image_markers > 0:
            print("✅ Image extraction working")
        elif len(images) == 0:
            print("⚠️  No images extracted (file may not contain images)")
        else:
            print("❌ Images extracted but no markers in text")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        logger.error(f"Extraction error: {e}")
        return False


def main():
    """Run all debug tests."""
    print("Image Marker Debug Test Suite")
    print("=" * 50)
    
    try:
        # Test the marker replacement logic
        marker_test = test_image_marker_replacement()
        
        # Test with real files if available
        extraction_test = test_image_extraction_only()
        conversion_test = test_real_docx_conversion()
        
        print("\n" + "=" * 50)
        print("Debug Test Results:")
        print(f"Marker replacement: {'✅ PASS' if marker_test else '❌ FAIL'}")
        print(f"Image extraction: {'✅ PASS' if extraction_test else '❌ FAIL'}")
        print(f"Full conversion: {'✅ PASS' if conversion_test else '❌ FAIL'}")
        
        if not marker_test:
            print("\n🔍 The issue is in the image marker replacement logic")
        elif not extraction_test:
            print("\n🔍 The issue is in the image extraction logic")
        elif not conversion_test:
            print("\n🔍 The issue is in the document conversion service integration")
        else:
            print("\n✅ All tests passed - image system should be working")
        
        return 0 if marker_test else 1
        
    except Exception as e:
        print(f"\n❌ Debug test suite failed: {e}")
        logger.error(f"Debug test suite error: {e}")
        return 1


if __name__ == "__main__":
    # Configure logging for debugging
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
