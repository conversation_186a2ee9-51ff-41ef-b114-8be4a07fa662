---
description: Maintains and updates the project's file tracking documentation
globs: ["**/*"]
alwaysApply: true
---

# File Tracker Rules

## Purpose

Maintain a comprehensive and up-to-date record of all project files and their purposes in the FILE_TRACKER.md document.

## Critical Rules

1. File Addition/Modification
   - Every new file must be documented in FILE_TRACKER.md
   - File purpose must be clearly described
   - File relationships must be documented
   - Changes to file purposes must be updated

2. Directory Structure
   - Maintain accurate directory tree representation
   - Document directory purposes
   - Track directory relationships
   - Update structure on directory changes

3. Module Documentation
   - Document module responsibilities
   - Track module dependencies
   - Maintain module relationship diagrams
   - Update on architectural changes

## File Tracker Format

### Directory Entry Format
```md
### /path/to/directory
Description of directory's purpose and contents.

#### Key Files
- `filename.ext`: Brief description of file purpose
- `another-file.ext`: What this file does
```

### Module Entry Format
```md
### Module Name
Purpose: Brief description of module's responsibility
Dependencies: List of other modules this depends on
Key Components:
- Component 1: Description
- Component 2: Description
```

## Update Triggers

1. File System Changes
   - New file creation
   - File deletion
   - File movement
   - Directory restructuring

2. Content Changes
   - Major functionality changes
   - Purpose changes
   - Dependency changes
   - Interface changes

## Validation Rules

1. Structure Validation
   - All files must be documented
   - Directory structure must be accurate
   - File purposes must be clear
   - Dependencies must be tracked

2. Content Validation
   - Descriptions must be meaningful
   - Relations must be accurate
   - Documentation must be current
   - Links must be valid

## Review Checklist

- [ ] All new files documented
- [ ] Directory structure accurate
- [ ] Module purposes clear
- [ ] Dependencies tracked
- [ ] Relationships documented
- [ ] Documentation current
