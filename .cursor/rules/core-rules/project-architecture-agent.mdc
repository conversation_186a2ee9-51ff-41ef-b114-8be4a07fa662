---
description: Enforces the 5-pillar architecture pattern across the project
globs: ["**/*"]
alwaysApply: true
---

# Project Architecture Rules

## 5-Pillar Architecture

Every major feature module must follow the 5-pillar structure:

1. Models
   - Data structures and types
   - Domain entities
   - Interfaces and type definitions

2. Repositories
   - Data access layer
   - Database interactions
   - External API integrations
   - Cache management

3. Services
   - Business logic
   - Data transformation
   - Complex operations
   - Service-to-service communication

4. UI (if applicable)
   - Components
   - State management
   - Event handlers
   - Styling

5. Utils
   - Helper functions
   - Common utilities
   - Shared constants
   - Type guards

## Directory Structure Rules

```
feature/
├── models/
│   ├── types.ts
│   ├── interfaces.ts
│   └── entities.ts
├── repositories/
│   ├── feature.repository.ts
│   └── interfaces.ts
├── services/
│   ├── feature.service.ts
│   └── interfaces.ts
├── ui/
│   ├── components/
│   └── hooks/
└── utils/
    ├── helpers.ts
    └── constants.ts
```

## Critical Rules

1. Each pillar must have clear responsibilities
2. No cross-pillar imports except through defined interfaces
3. Services must not directly access repositories of other features
4. UI components must only interact with services through facades
5. Utils must be pure functions without side effects

## Best Practices

1. Models
   - Use interfaces over classes when possible
   - Keep models focused and single-purpose
   - Include validation rules

2. Repositories
   - Abstract data access behind interfaces
   - Handle all data persistence
   - Include error handling

3. Services
   - Implement business logic only
   - Use dependency injection
   - Handle complex operations

4. UI
   - Keep components small and focused
   - Separate business logic from presentation
   - Use composition over inheritance

5. Utils
   - Keep utilities generic and reusable
   - Document edge cases
   - Include unit tests

## Validation Checklist

- [ ] Feature follows 5-pillar structure
- [ ] Each pillar has clear responsibilities
- [ ] Interfaces properly defined
- [ ] No circular dependencies
- [ ] Proper error handling
- [ ] Documentation complete
- [ ] Tests implemented
