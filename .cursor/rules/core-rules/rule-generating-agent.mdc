---
description: Ensures consistent rule creation and maintenance across the project
globs: ["**/*.mdc"]
alwaysApply: true
---

# Rule Generation Standards

## Rule File Structure

1. Frontmatter Requirements
   - description: Clear purpose statement
   - globs: File patterns this rule applies to
   - alwaysApply: Boolean for global application

2. Section Requirements
   - Purpose/Overview section
   - Critical Rules section
   - Validation Rules section
   - Examples section (when applicable)
   - Review Checklist

## Rule Writing Guidelines

1. Clarity
   - Use clear, specific language
   - Avoid ambiguity
   - Include examples for complex rules
   - Define terms when necessary

2. Consistency
   - Follow standard format
   - Use consistent terminology
   - Maintain consistent structure
   - Use standard markdown formatting

3. Completeness
   - Cover all edge cases
   - Include validation criteria
   - Specify dependencies
   - Document exceptions

## Rule Categories

1. Core Rules
   - Project architecture
   - File organization
   - Documentation standards
   - Development workflow

2. Language-Specific Rules
   - Coding standards
   - Best practices
   - Common patterns
   - Error handling

3. Tool-Specific Rules
   - Usage guidelines
   - Configuration standards
   - Integration patterns
   - Error resolution

## Rule Templates

### Basic Rule Template
```md
---
description: Brief description of rule purpose
globs: ["pattern/**/*"]
alwaysApply: false
---

# Rule Title

## Purpose
Clear statement of rule's purpose

## Critical Rules
1. First rule
2. Second rule
   - Sub-rule
   - Sub-rule

## Validation Rules
- Validation criterion 1
- Validation criterion 2

## Examples
Example implementation or usage

## Review Checklist
- [ ] Checklist item 1
- [ ] Checklist item 2
```

## Validation Rules

1. Structure Validation
   - Required sections present
   - Proper frontmatter format
   - Consistent heading levels
   - Valid markdown syntax

2. Content Validation
   - Clear purpose statement
   - Specific, actionable rules
   - Relevant examples
   - Complete validation criteria

3. Integration Validation
   - No conflicting rules
   - Proper rule precedence
   - Compatible requirements
   - Clear dependencies

## Review Checklist

- [ ] Frontmatter complete and valid
- [ ] All required sections present
- [ ] Rules are clear and specific
- [ ] Examples provided where needed
- [ ] Validation criteria defined
- [ ] No conflicts with existing rules
- [ ] Proper markdown formatting
- [ ] Consistent terminology used
