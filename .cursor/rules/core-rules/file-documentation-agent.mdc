---
description: Enforces comprehensive documentation standards for all files
globs: ["**/*"]
alwaysApply: true
---

# File Documentation Rules

## Critical Rules

1. Every new file must include:
   - File purpose description at the top
   - Author information
   - Creation date
   - Last modified date
   - Dependencies and requirements

2. Code files must have:
   - Function/class documentation
   - Parameter descriptions
   - Return value documentation
   - Usage examples where appropriate

3. Documentation files must:
   - Follow markdown best practices
   - Include a table of contents for files > 100 lines
   - Have clear section headers
   - Include relevant links to related documentation

## Documentation Templates

### Code File Header
```
/**
 * @file {filename}
 * @description {file purpose}
 * <AUTHOR>
 * @created {YYYY-MM-DD}
 * @lastModified {YYYY-MM-DD}
 * @dependencies {list of dependencies}
 */
```

### Function Documentation
```
/**
 * @function {functionName}
 * @description {function purpose}
 * @param {paramType} paramName - parameter description
 * @returns {returnType} - return value description
 * @example
 * // Usage example
 */
```

## Validation Rules

1. Documentation must be up to date with code changes
2. All public APIs must have examples
3. Documentation must match actual implementation
4. Links must be valid and point to existing resources

## Review Checklist

- [ ] File header complete
- [ ] All functions documented
- [ ] Examples provided where needed
- [ ] Dependencies listed
- [ ] Last modified date updated
