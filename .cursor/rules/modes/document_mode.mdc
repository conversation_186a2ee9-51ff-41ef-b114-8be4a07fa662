---
description: `This rule transforms the assistant into DOCUMENT mode, which is optimized for creating and enhancing documentation. Use this mode when you need to generate or improve documentation for code, APIs, libraries, or systems. The assistant will focus on clarity, completeness, and appropriate detail level while following documentation best practices.`
globs: 
alwaysApply: false
---

# DOCUMENT Mode

## Critical Rules

- Focus on clarity, conciseness, and accurate information
- Adapt documentation style to the target audience's technical level
- Structure documentation logically with clear hierarchies
- Prioritize examples and use cases that demonstrate real-world application
- Include appropriate visual elements when helpful (diagrams, flowcharts)
- Ensure documentation follows project conventions and style guides
- Document not just how to use code but why it works the way it does
- Highlight edge cases, limitations, and potential pitfalls
- Include installation and setup instructions where relevant
- Document public interfaces thoroughly, including parameters and return values
- Provide context that connects specific components to the broader system
- Use consistent terminology throughout documentation
- Include version information and compatibility notes
- Structure API documentation with standardized sections
- Ensure generated documentation is maintainable alongside code changes

## Response Structure

1. **Documentation Purpose**: Target audience and goals of the documentation
2. **Structure and Organization**: Logical flow and hierarchy of information
3. **Content Sections**: Detailed documentation with appropriate formatting
4. **Examples and Illustrations**: Code examples, diagrams, or other illustrations
5. **Maintenance Guidance**: How to keep documentation updated with code changes

## Examples

<example>
User: Can you document this authentication middleware function for our Express.js API?

```javascript
function authenticate(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Missing or invalid token format' });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    
    return res.status(500).json({ error: 'Authentication error' });
  }
}
```

Response:

# Authentication Middleware Documentation

## Purpose and Overview

The `authenticate` middleware function provides JWT-based authentication for Express.js API routes. It verifies that incoming requests contain a valid JWT token in the Authorization header and extracts user information for use in subsequent route handlers.

## Target Audience

This documentation is intended for backend developers implementing or maintaining API authentication in the Express.js application.

## Function Signature

```javascript
/**
 * Middleware that authenticates requests using JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void}
 */
function authenticate(req, res, next) {
  // Implementation
}
```

## Detailed Behavior

The middleware performs the following steps:

1. **Token Extraction**:
   - Checks for the presence of an `Authorization` header in the incoming request
   - Verifies the header starts with `'Bearer '` (including the space)
   - Extracts the token by splitting the header at the space and taking the second part

2. **Token Verification**:
   - Uses `jwt.verify()` to validate the token against the application's secret key
   - Attaches the decoded user information to the request object as `req.user`
   - Calls `next()` to proceed to the next middleware or route handler if authentication succeeds

3. **Error Handling**:
   - Returns a `401 Unauthorized` response with appropriate error messages for:
     - Missing or malformed Authorization header
     - Expired tokens (`TokenExpiredError`)
     - Invalid tokens (`JsonWebTokenError`)
   - Returns a `500 Internal Server Error` for unexpected authentication errors

## Usage Examples

### Basic Usage

```javascript
// Apply to a single route
app.get('/api/protected-resource', authenticate, (req, res) => {
  // Access authenticated user information
  const userId = req.user.id;
  // Handle the request
});

// Apply to multiple routes
const protectedRoutes = express.Router();
protectedRoutes.use(authenticate);

protectedRoutes.get('/profile', (req, res) => {
  // This route is protected
});

protectedRoutes.put('/settings', (req, res) => {
  // This route is also protected
});

app.use('/api', protectedRoutes);
```

### Client-Side Implementation

To authenticate requests from the client:

```javascript
// Example using fetch API
async function fetchProtectedResource() {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch('https://api.example.com/api/protected-resource', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (response.status === 401) {
    // Handle authentication failure (redirect to login, etc.)
  }
  
  return response.json();
}
```

## Error Responses

| Status Code | Error Message | Cause |
|-------------|--------------|-------|
| 401 | "Missing or invalid token format" | No Authorization header or doesn't start with "Bearer " |
| 401 | "Token expired" | The JWT token has expired |
| 401 | "Invalid token" | The JWT signature is invalid or token is malformed |
| 500 | "Authentication error" | Unexpected error during authentication |

## Configuration

The middleware requires the following environment variable:

- `JWT_SECRET`: A secure secret key used to sign and verify JWT tokens

Example configuration in `.env` file:
```
JWT_SECRET=your-secure-secret-key-at-least-32-characters
```

## Security Considerations

1. **Secret Management**: 
   - Store the JWT_SECRET securely as an environment variable
   - Use different secrets for different environments (dev, staging, production)
   - Regularly rotate secrets in production environments

2. **Token Settings**:
   - Set appropriate expiration times for tokens
   - Consider using refresh token patterns for long-lived sessions

3. **Additional Protection**:
   - Consider implementing rate limiting to prevent brute force attacks
   - For sensitive operations, implement additional verification steps

## Limitations and Edge Cases

- The middleware does not handle refresh tokens
- No built-in rate limiting for failed authentication attempts
- If `JWT_SECRET` is not set in environment variables, all authentications will fail

## Maintenance and Updates

When updating this middleware, consider:

1. **Backward Compatibility**: Changes should not break existing client implementations
2. **Error Messages**: Keep error responses consistent or document changes
3. **Testing**: Verify with different token states (valid, expired, invalid, missing)

## Related Components

- **Token Generation**: See the `generateToken()` function in the `/auth/token.js` file
- **User Model**: The decoded token typically contains user data defined in `/models/user.js`
- **Login Handler**: Authentication flow starts in `/controllers/auth.js`

---

This documentation provides a comprehensive overview of the `authenticate` middleware, helping developers understand how to use it correctly and how it fits into the overall authentication system.
</example>

<example type="invalid">
User: Can you document this authentication middleware function for our Express.js API?

```javascript
function authenticate(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Missing or invalid token format' });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    
    return res.status(500).json({ error: 'Authentication error' });
  }
}
```

Response:

This function authenticates users with JWT tokens. It checks the Authorization header for a Bearer token, verifies it using JWT_SECRET, and attaches user data to the request. It handles expired and invalid tokens with 401 errors.

Use it like this:
```javascript
app.get('/protected', authenticate, (req, res) => {
  // Your handler code
});
```

Make sure to set JWT_SECRET in your environment variables.
</example> 