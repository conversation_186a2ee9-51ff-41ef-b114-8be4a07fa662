#!/usr/bin/env python3
"""
Test script to verify the image marker fix.

This script tests that HTML content with images is loaded correctly
and that image markers are not converted to plain text.
"""

import sys
import re
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_html_to_plain_text_with_images():
    """Test the HTML to plain text conversion with images."""
    print("=== Testing HTML to Plain Text Conversion with Images ===")
    
    # Create test HTML content with images
    test_html = """<html><body>
<h1>Test Document</h1>
<p>This is the first paragraph.</p>
<div style="text-align: center; margin: 10px 0;">
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" 
         alt="Test Image 1" 
         style="max-width: 100px; max-height: 100px; width: auto; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
         title="Test Image 1" />
</div>
<p>This is text after the first image.</p>
<h2>Section 2</h2>
<p>Some more content here.</p>
<div style="text-align: center; margin: 10px 0;">
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" 
         alt="Test Image 2" 
         style="max-width: 200px; max-height: 150px; width: auto; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
         title="Test Image 2" />
</div>
<p>Final paragraph.</p>
</body></html>"""
    
    print(f"Test HTML content ({len(test_html)} chars):")
    print(test_html[:200] + "..." if len(test_html) > 200 else test_html)
    
    # Test the HTML to plain text conversion function
    def html_to_plain_text(html_content: str) -> str:
        """Test version of the HTML to plain text conversion."""
        try:
            # Remove script and style elements completely
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

            # Convert common HTML elements to text equivalents
            html_content = re.sub(r'<br\s*/?>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<p[^>]*>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</p>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<h[1-6][^>]*>', '\n\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</h[1-6]>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<li[^>]*>', '\n• ', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</li>', '', html_content, flags=re.IGNORECASE)

            # Handle images specially - convert to placeholder text
            html_content = re.sub(
                r'<img[^>]*alt=["\']([^"\']*)["\'][^>]*>',
                r'\n[IMAGE: \1]\n',
                html_content,
                flags=re.IGNORECASE
            )
            # Handle images without alt text
            html_content = re.sub(
                r'<img[^>]*>',
                r'\n[IMAGE]\n',
                html_content,
                flags=re.IGNORECASE
            )

            # Remove all remaining HTML tags (except images which are already handled)
            html_content = re.sub(r'<[^>]+>', '', html_content)

            # Decode HTML entities
            html_content = html_content.replace('&amp;', '&')
            html_content = html_content.replace('&lt;', '<')
            html_content = html_content.replace('&gt;', '>')
            html_content = html_content.replace('&quot;', '"')
            html_content = html_content.replace('&#x27;', "'")
            html_content = html_content.replace('&nbsp;', ' ')

            # Clean up whitespace
            html_content = re.sub(r'\n\s*\n\s*\n', '\n\n', html_content)  # Remove excessive line breaks
            html_content = re.sub(r'[ \t]+', ' ', html_content)  # Normalize spaces
            html_content = html_content.strip()

            return html_content

        except Exception as e:
            print(f"Error converting HTML to plain text: {e}")
            # Last resort - just remove tags
            return re.sub(r'<[^>]+>', '', html_content)
    
    # Convert to plain text
    plain_text = html_to_plain_text(test_html)
    
    print(f"\nConverted plain text ({len(plain_text)} chars):")
    print(plain_text)
    
    # Check results
    print("\n--- Analysis ---")
    
    # Count images in HTML
    html_img_count = test_html.count('<img ')
    print(f"Images in HTML: {html_img_count}")
    
    # Count image placeholders in plain text
    image_placeholder_count = plain_text.count('[IMAGE:')
    print(f"Image placeholders in plain text: {image_placeholder_count}")
    
    # Check for specific image placeholders
    if '[IMAGE: Test Image 1]' in plain_text:
        print("✅ Found placeholder for Test Image 1")
    else:
        print("❌ Missing placeholder for Test Image 1")
    
    if '[IMAGE: Test Image 2]' in plain_text:
        print("✅ Found placeholder for Test Image 2")
    else:
        print("❌ Missing placeholder for Test Image 2")
    
    # Overall result
    if image_placeholder_count == html_img_count:
        print("✅ HTML to plain text conversion preserves image information")
        return True
    else:
        print("❌ HTML to plain text conversion has issues")
        return False


def test_image_detection():
    """Test the image detection logic."""
    print("\n=== Testing Image Detection Logic ===")
    
    test_cases = [
        ("<html><body><p>No images here</p></body></html>", False),
        ("<html><body><p>Text with <img src='test.jpg'> image</p></body></html>", True),
        ("<html><body><IMG SRC='test.png' ALT='test'></body></html>", True),  # Case insensitive
        ("<html><body><p>Just text</p></body></html>", False),
        ("<html><body><div><img alt='test'/></div></body></html>", True),
    ]
    
    print("Image detection test cases:")
    for i, (html, expected) in enumerate(test_cases):
        has_images = '<img ' in html.lower()
        status = "✅" if has_images == expected else "❌"
        print(f"  {status} Test {i+1}: Expected {expected}, Got {has_images}")
        print(f"      HTML: {html[:50]}...")
    
    return True


def test_size_threshold_logic():
    """Test the size threshold logic for HTML loading."""
    print("\n=== Testing Size Threshold Logic ===")
    
    # Test different content sizes and image presence
    test_cases = [
        (50000, False, "Direct load (small, no images)"),
        (50000, True, "Direct load (small, has images)"),
        (150000, False, "Convert to plain text (large, no images)"),
        (150000, True, "Direct load (large, has images)"),  # This is the key fix
    ]
    
    print("Size threshold test cases:")
    for size, has_images, expected_behavior in test_cases:
        # Simulate the logic from _load_html_safely
        should_load_directly = size < 100000 or has_images
        
        if should_load_directly:
            actual_behavior = "Direct load"
        else:
            actual_behavior = "Convert to plain text"
        
        status = "✅" if expected_behavior.startswith(actual_behavior) else "❌"
        print(f"  {status} Size: {size:,}, Has images: {has_images}")
        print(f"      Expected: {expected_behavior}")
        print(f"      Actual: {actual_behavior}")
    
    return True


def main():
    """Run all tests."""
    print("Image Fix Verification Test Suite")
    print("=" * 50)
    
    try:
        # Test the HTML to plain text conversion
        html_test = test_html_to_plain_text_with_images()
        
        # Test image detection
        detection_test = test_image_detection()
        
        # Test size threshold logic
        threshold_test = test_size_threshold_logic()
        
        print("\n" + "=" * 50)
        print("Test Results:")
        print(f"HTML to plain text: {'✅ PASS' if html_test else '❌ FAIL'}")
        print(f"Image detection: {'✅ PASS' if detection_test else '❌ FAIL'}")
        print(f"Size threshold: {'✅ PASS' if threshold_test else '❌ FAIL'}")
        
        if all([html_test, detection_test, threshold_test]):
            print("\n✅ All tests passed!")
            print("\nThe fix should resolve the image marker issue:")
            print("1. HTML with images will be loaded directly (not converted to plain text)")
            print("2. If conversion to plain text is needed, images become readable placeholders")
            print("3. Image markers should no longer appear as literal text in the RTF Editor")
        else:
            print("\n❌ Some tests failed - the fix may need adjustment")
        
        return 0 if all([html_test, detection_test, threshold_test]) else 1
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
