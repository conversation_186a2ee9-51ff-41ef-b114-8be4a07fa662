# Knowledge Base Import Debugging Guide

## Overview

This guide explains the comprehensive debugging system we've implemented to identify and resolve freezing issues during the Knowledge Base document import process.

## What We've Added

### 1. Enhanced Document Import Debugging (`document_manager/ui/document_tab.py`)

#### Main Import Handler (`_handle_documents_converted`)
- **Timing Measurements**: Tracks total import time and per-document processing time
- **Memory Monitoring**: Monitors memory usage before, during, and after each document
- **Detailed Logging**: Logs document attributes, content sizes, and processing steps
- **Error Tracking**: Captures exact timing and context when errors occur

#### Safe Document Loading (`_load_document_safely`)
- **Step-by-step Timing**: Times each phase of document loading
- **Content Analysis**: Logs content sizes and types before processing
- **Mode-specific Debugging**: Separate debugging for HTML, plain text, and truncated modes
- **Memory Tracking**: Monitors memory usage throughout the loading process

#### HTML Loading (`_load_html_safely`)
- **Size-based Processing**: Different handling for small vs. large HTML content
- **Conversion Timing**: Times HTML-to-plain-text conversion
- **Fallback Monitoring**: Tracks when and why fallbacks are used

### 2. Knowledge Base Panel (`knowledge_base/ui/knowledge_base_panel.py`)

#### New UI Components
- **Knowledge Base Button**: Added to the Documents pillar for easy access
- **Import & Debug Tab**: Dedicated interface for testing import functionality
- **Debug Tab**: Real-time monitoring and testing tools
- **Import Log**: Detailed logging of import operations
- **Progress Indicators**: Visual feedback during import operations

#### Debug Features
- **Memory Usage Monitoring**: Real-time memory usage display
- **Import Process Testing**: Simulates document conversion and loading
- **Document Size Analysis**: Tests with various document sizes
- **Performance Metrics**: Timing and memory delta tracking

### 3. Test Suite (`test_knowledge_base_debug.py`)

#### Comprehensive Testing
- **Memory Monitoring Test**: Verifies memory tracking functionality
- **Document Format Simulation**: Tests with various document sizes
- **Timing Measurements**: Validates timing accuracy
- **UI Component Testing**: Ensures Knowledge Base panel works correctly

## How to Use the Debugging System

### 1. Access the Knowledge Base Panel
1. Open the Documents pillar in IsopGem
2. Click the "Knowledge Base" button
3. Navigate to the "Import & Debug" tab

### 2. Monitor Import Process
1. Use the "Import from RTF Editor" button to test import functionality
2. Watch the Import Log for detailed step-by-step information
3. Monitor memory usage in real-time
4. Check for warnings about large document sizes

### 3. Debug Freezing Issues
When experiencing freezing during import:

1. **Check the Application Logs**:
   - Look for timing information that stops abruptly
   - Identify the last successful step before freezing
   - Check memory usage patterns

2. **Use the Debug Tab**:
   - Run "Test Import Process" to simulate the issue
   - Monitor memory usage with "Check Memory Usage"
   - Compare results with working imports

3. **Analyze the Import Log**:
   - Look for size warnings (HTML > 200KB, Plain text > 500KB)
   - Check for validation failures
   - Identify slow operations (> 1 second)

### 4. Key Debug Information

#### Timing Breakdowns
- **Document Creation**: RTF Editor window creation time
- **Content Loading**: Time to load content into editor
- **Window Management**: Time to open window via window manager
- **HTML Processing**: HTML validation and conversion time
- **Memory Operations**: Memory allocation and cleanup time

#### Memory Monitoring
- **RSS (Resident Set Size)**: Actual memory usage
- **Memory Deltas**: Memory changes per operation
- **Peak Usage**: Maximum memory during import
- **Cleanup Verification**: Memory release after operations

#### Content Analysis
- **Document Attributes**: Available content types and sizes
- **Size Limits**: Warnings when content exceeds safe limits
- **Validation Results**: HTML structure and content validation
- **Fallback Usage**: When and why fallbacks are triggered

## Common Freezing Scenarios and Solutions

### 1. Large HTML Content (> 200KB)
**Symptoms**: Freezing during HTML loading
**Debug Info**: Look for "HTML content too large" warnings
**Solution**: Content is automatically converted to plain text

### 2. Excessive Memory Usage
**Symptoms**: Gradual slowdown then freeze
**Debug Info**: Large memory deltas in logs
**Solution**: Monitor memory patterns to identify leaks

### 3. Invalid HTML Structure
**Symptoms**: Freezing during HTML validation
**Debug Info**: "Invalid HTML content detected" messages
**Solution**: Automatic fallback to plain text

### 4. Window Manager Issues
**Symptoms**: Freezing after document loading
**Debug Info**: Long "Window manager open" times
**Solution**: Check window manager state and resources

## Log Analysis Examples

### Normal Operation
```
[14:23:15] === IMPORT DEBUG START ===
[14:23:15] Document 1/1 DEBUG: Test Document
[14:23:15] HTML content size: 150,000 characters (146.48 KB)
[14:23:15] RTF Editor creation took: 0.045 seconds
[14:23:15] Document loading took: 0.123 seconds
[14:23:15] Window manager open took: 0.067 seconds
[14:23:15] Document 1 completed in: 0.235 seconds
[14:23:15] === IMPORT DEBUG COMPLETE ===
```

### Problematic Operation
```
[14:25:30] === IMPORT DEBUG START ===
[14:25:30] Document 1/1 DEBUG: Large Document
[14:25:30] HTML content size: 2,500,000 characters (2.38 MB)
[14:25:30] WARNING: HTML content exceeds size limit
[14:25:30] RTF Editor creation took: 0.052 seconds
[14:25:30] Document loading took: 15.234 seconds  <-- PROBLEM
[14:25:45] LOAD ERROR: Memory allocation failed
```

## Dependencies

The debugging system requires:
- `psutil>=5.9.0` for memory monitoring (added to requirements.txt)
- `time` module for timing measurements (built-in)
- `loguru` for enhanced logging (already included)

## Next Steps

1. **Test the System**: Run `python test_knowledge_base_debug.py` to verify functionality
2. **Reproduce the Issue**: Use the Knowledge Base panel to import documents and monitor for freezing
3. **Analyze Results**: Use the debug information to identify the specific cause of freezing
4. **Implement Fixes**: Based on debug findings, implement targeted solutions

The debugging system provides comprehensive visibility into the import process, making it much easier to identify and resolve freezing issues.
