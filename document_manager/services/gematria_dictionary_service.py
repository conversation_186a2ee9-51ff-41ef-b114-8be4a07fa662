"""
Gematria Dictionary Service.

This module provides functionality for analyzing documents and creating
gematria dictionaries that list unique words with their calculated values.

Author: Assistant
Created: 2024-12-28
Last Modified: 2024-12-28
Dependencies: re, typing, loguru, gematria services
"""

import re
from typing import Dict, List, Optional, Set, Tuple
from loguru import logger

from gematria.models.calculation_type import CalculationType, Language, language_from_text
from gematria.services.gematria_service import GematriaService
from document_manager.models.gematria_dictionary_entry import (
    GematriaDictionaryEntry, 
    GematriaDictionaryResult,
    VerseAnalysisEntry,
    VerseWordEntry,
    VerseBreakdown,
    GlobalAnalysisSummary,
    ChapterSummary,
    TextStructureType,
    LineAnalysisEntry,
    TextStructureAnalysis
)


class GematriaDictionaryService:
    """Service for creating gematria dictionaries from documents."""
    
    def __init__(self):
        """Initialize the service."""
        self.gematria_service = GematriaService()
        
        # Default calculation types for each language
        self.default_calculation_types = {
            Language.HEBREW: [
                CalculationType.HEBREW_STANDARD_VALUE
            ],
            Language.GREEK: [
                CalculationType.GREEK_STANDARD_VALUE
            ],
            Language.ENGLISH: [
                CalculationType.ENGLISH_TQ_STANDARD_VALUE
            ]
        }
    
    def analyze_document(
        self, 
        text: str, 
        document_title: str = "Untitled Document",
        custom_calculation_types: Optional[Dict[Language, List[CalculationType]]] = None
    ) -> GematriaDictionaryResult:
        """
        Analyze a document and create a gematria dictionary.
        
        Args:
            text: The document text to analyze
            document_title: Title of the document
            custom_calculation_types: Custom calculation types per language
            
        Returns:
            GematriaDictionaryResult containing the analysis
        """
        logger.info(f"Starting gematria dictionary analysis for: {document_title}")
        
        # Extract words and their positions first to detect languages
        words_data = self._extract_words_with_verses(text)
        
        # Detect which languages are actually present in the document
        languages_present = set()
        for word, verse in words_data:
            # Skip numbers and ampersands for language detection
            if not word.isdigit() and word != '&':
                language = self._detect_word_language(self._normalize_word(word))
                languages_present.add(language)
        
        # Create calculation types only for languages actually present
        if custom_calculation_types:
            calc_types = custom_calculation_types
        else:
            calc_types = {}
            for language in languages_present:
                if language in self.default_calculation_types:
                    calc_types[language] = self.default_calculation_types[language]
                elif language == Language.UNKNOWN:
                    # For unknown words, use English TQ if available
                    calc_types[Language.ENGLISH] = self.default_calculation_types.get(Language.ENGLISH, [])
                    # Remove the UNKNOWN language to avoid duplicates
                    languages_present.discard(Language.UNKNOWN)
        
        # Group words by normalized form
        word_groups = self._group_words(words_data)
        
        # Create dictionary entries
        entries = []
        calculation_types_used = set()
        
        for normalized_word, word_info in word_groups.items():
            # Skip if word is empty or just whitespace
            if not normalized_word.strip():
                continue
                
            # Detect language
            language = self._detect_word_language(normalized_word)
            
            # Get calculation types for this language
            lang_calc_types = calc_types.get(language, [])
            if not lang_calc_types:
                # If no specific types for this language, try TQ for unknown words
                if language == Language.UNKNOWN:
                    lang_calc_types = calc_types.get(Language.ENGLISH, [])
                    language = Language.ENGLISH
            
            # Calculate gematria values
            gematria_values = {}
            for calc_type in lang_calc_types:
                try:
                    value = self.gematria_service.calculate(normalized_word, calc_type)
                    gematria_values[calc_type.display_name] = value
                    calculation_types_used.add(calc_type.display_name)
                except Exception as e:
                    logger.warning(f"Failed to calculate {calc_type.display_name} for '{normalized_word}': {e}")
            
            # Create entry
            entry = GematriaDictionaryEntry(
                word=word_info['original_word'],
                normalized_word=normalized_word,
                language=language,
                gematria_values=gematria_values,
                frequency=word_info['frequency'],
                occurrences=word_info['occurrences'],
                verse_numbers=word_info['verse_numbers']
            )
            entries.append(entry)
        
        # Sort entries by word alphabetically
        entries.sort(key=lambda x: x.normalized_word.lower())
        
        # Analyze text structure first
        structure_analysis = self._analyze_text_structure(text)
        logger.info(f"Detected text structure: {structure_analysis.structure_type.value} (confidence: {structure_analysis.confidence:.2f})")
        
        # Perform analysis based on detected structure
        verse_analysis = []
        line_analysis = []
        chapter_summaries = []
        
        logger.debug(f"Structure analysis detected: {structure_analysis.structure_type.value}")

        if structure_analysis.structure_type == TextStructureType.VERSE_NUMBERED:
            # Traditional verse analysis
            logger.debug("Performing verse analysis")
            verse_analysis = self._analyze_verses(text, calc_types)
            logger.debug(f"Verse analysis completed: {len(verse_analysis)} verses found")
            chapter_summaries = self._create_chapter_summaries(verse_analysis)
            global_analysis = self._calculate_global_analysis(verse_analysis)
        else:
            # Line-by-line analysis for non-verse texts
            logger.debug("Performing line analysis (no verse structure detected)")
            line_analysis = self._analyze_lines(text, calc_types, structure_analysis)
            # Create global analysis from line analysis
            global_analysis = self._calculate_global_analysis_from_lines(line_analysis)
        
        result = GematriaDictionaryResult(
            document_title=document_title,
            total_words=len(words_data),
            unique_words=len(entries),
            entries=entries,
            calculation_types_used=sorted(list(calculation_types_used)),
            verse_analysis=verse_analysis,
            line_analysis=line_analysis,
            text_structure=structure_analysis,
            chapter_summaries=chapter_summaries,
            global_analysis=global_analysis
        )
        
        logger.info(f"Analysis complete: {result.total_words} total words, {result.unique_words} unique words")
        return result
    
    def _extract_words_with_verses(self, text: str) -> List[Tuple[str, Optional[str]]]:
        """Extract words from text with their associated verse numbers."""
        words_with_verses = []
        lines = text.split('\n')
        current_verse = None
        
        for line_num, line in enumerate(lines):
            # Skip empty lines
            if not line.strip():
                continue
            
            # Check if this is a chapter heading (e.g., "Chapter 1", "Chapter 2")
            if self._is_chapter_heading(line.strip()):
                # Skip chapter headings - don't include them in word extraction
                continue
                
            # Check if line starts with a verse number (handles formats like \"1.\" or \"1 \" or just \"1\")
            verse_match = re.match(r'^(\d+)[\.\s]', line.strip())
            if verse_match:
                current_verse = verse_match.group(1)
                # Remove verse numbers from the beginning of lines for word extraction
                # This handles formats like \"1. text\", \"1 text\", or \"1\\ttext\"
                line_for_words = re.sub(r'^\d+[\.\s]+', '', line)
            else:
                # This is a continuation line - use the current verse context
                line_for_words = line
            
            # Extract words and numbers from the line (excluding verse numbers)
            words_and_numbers = self._extract_words_and_numbers_from_line(line_for_words)
            
            # Add words with their verse number
            for item in words_and_numbers:
                words_with_verses.append((item, current_verse))
        
        return words_with_verses
    
    def _is_chapter_heading(self, line: str) -> bool:
        """
        Check if a line is a chapter heading.
        
        Args:
            line: The line to check
            
        Returns:
            True if the line is a chapter heading
        """
        # Common chapter heading patterns
        chapter_patterns = [
            r'^Chapter\s+\d+$',           # "Chapter 1", "Chapter 2", etc.
            r'^CHAPTER\s+\d+$',           # "CHAPTER 1", "CHAPTER 2", etc.
            r'^Ch\.\s*\d+$',              # "Ch. 1", "Ch.1", etc.
            r'^Ch\s+\d+$',                # "Ch 1", "Ch 2", etc.
            r'^\d+\.\s*Chapter$',         # "1. Chapter", "2. Chapter", etc.
            r'^Part\s+\d+$',              # "Part 1", "Part 2", etc.
            r'^PART\s+\d+$',              # "PART 1", "PART 2", etc.
            r'^Book\s+\d+$',              # "Book 1", "Book 2", etc.
            r'^BOOK\s+\d+$',              # "BOOK 1", "BOOK 2", etc.
        ]
        
        for pattern in chapter_patterns:
            if re.match(pattern, line.strip(), re.IGNORECASE):
                return True
        
        return False
    
    def _extract_chapter_number(self, line: str) -> Optional[int]:
        """
        Extract chapter number from a chapter heading.
        
        Args:
            line: The chapter heading line
            
        Returns:
            Chapter number if found, None otherwise
        """
        # Extract number from various chapter heading formats
        patterns = [
            r'Chapter\s+(\d+)',
            r'CHAPTER\s+(\d+)',
            r'Ch\.?\s*(\d+)',
            r'(\d+)\.\s*Chapter',
            r'Part\s+(\d+)',
            r'PART\s+(\d+)',
            r'Book\s+(\d+)',
            r'BOOK\s+(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line.strip(), re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        return None
    
    def _extract_words_from_line(self, line: str) -> List[str]:
        """Extract words from a single line of text."""
        # Find all words in the line (including Hebrew, Greek, and English)
        # This pattern matches sequences of letters from various scripts
        # Updated pattern to properly handle contractions and possessives like "herald's", "don't", etc.
        word_pattern = r"[\u0590-\u05FF\u0370-\u03FF\u1F00-\u1FFFa-zA-Z]+(?:['\-][\u0590-\u05FF\u0370-\u03FF\u1F00-\u1FFFa-zA-Z]+)*"

        words = []
        for match in re.finditer(word_pattern, line):
            word = match.group()

            # Skip very short words (single characters) unless they're meaningful
            if len(word.strip()) < 2 and not self._is_meaningful_single_char(word):
                continue

            words.append(word)

        return words
    
    def _extract_words_and_numbers_from_line(self, line: str) -> List[str]:
        """Extract both words and numbers from a single line of text."""
        # Combined pattern for words, numbers, and ampersands
        # This preserves the order as they appear in the text
        # Updated pattern to properly handle contractions and possessives like "herald's", "don't", etc.
        combined_pattern = r"[\u0590-\u05FF\u0370-\u03FF\u1F00-\u1FFFa-zA-Z]+(?:['\-][\u0590-\u05FF\u0370-\u03FF\u1F00-\u1FFFa-zA-Z]+)*|\b\d+\b|&"

        items = []

        # Find all items in order
        for match in re.finditer(combined_pattern, line):
            item = match.group()

            # Skip very short words (single characters) unless they're meaningful
            # But always include numbers and ampersands
            if item.isdigit() or item == '&':
                items.append(item)
            elif len(item.strip()) < 2 and not self._is_meaningful_single_char(item):
                continue
            else:
                items.append(item)

        return items
    
    def _is_meaningful_single_char(self, char: str) -> bool:
        """
        Check if a single character is meaningful for gematria analysis.
        
        Args:
            char: The character to check
            
        Returns:
            True if the character is meaningful
        """
        # Hebrew and Greek single letters are always meaningful
        hebrew_range = '\u0590-\u05FF'
        greek_range = '\u0370-\u03FF\u1F00-\u1FFF'
        
        # Check for Hebrew or Greek characters
        if re.match(f'[{hebrew_range}{greek_range}]', char):
            return True
        
        # ALL English/Latin alphabetic characters are meaningful for gematria
        # This includes A-Z and a-z
        if char.isalpha():
            return True
        
        return False
    
    def _group_words(self, words_data: List[Tuple[str, Optional[str]]]) -> Dict[str, Dict]:
        """
        Group words by their normalized form.
        
        Args:
            words_data: List of tuples containing word and verse information
            
        Returns:
            Dictionary mapping normalized words to their information
        """
        word_groups = {}
        
        for word, verse in words_data:
            normalized = self._normalize_word(word)
            
            if normalized not in word_groups:
                word_groups[normalized] = {
                    'original_word': word,
                    'frequency': 0,
                    'occurrences': [],
                    'verse_numbers': []
                }
            
            # Update information
            word_groups[normalized]['frequency'] += 1
            word_groups[normalized]['occurrences'].append(word)
            
            if verse is not None:
                verse_num = int(verse)
                if verse_num not in word_groups[normalized]['verse_numbers']:
                    word_groups[normalized]['verse_numbers'].append(verse_num)
        
        return word_groups
    
    def _normalize_word(self, word: str) -> str:
        """
        Normalize a word for grouping purposes.
        
        Args:
            word: The word to normalize
            
        Returns:
            Normalized word
        """
        # Remove common punctuation and normalize case
        normalized = word.strip().lower()
        
        # Remove common punctuation marks
        normalized = re.sub(r'[.,;:!?"\'\-\u2019\u2018\u201C\u201D]', '', normalized)
        
        # For Hebrew and Greek, we might want to remove diacritical marks
        # but keep the base characters
        
        return normalized
    
    def _detect_word_language(self, word: str) -> Language:
        """
        Detect the language of a word.
        
        Args:
            word: The word to analyze
            
        Returns:
            Detected language
        """
        # Use the existing language detection from gematria service
        detected = language_from_text(word)
        return detected if detected else Language.UNKNOWN
    
    def get_words_by_value(
        self, 
        result: GematriaDictionaryResult, 
        target_value: int, 
        calculation_type: str
    ) -> List[GematriaDictionaryEntry]:
        """
        Find all words with a specific gematria value.
        
        Args:
            result: The dictionary result to search
            target_value: The gematria value to find
            calculation_type: The calculation type to check
            
        Returns:
            List of entries with the target value
        """
        matching_entries = []
        
        for entry in result.entries:
            if calculation_type in entry.gematria_values:
                if entry.gematria_values[calculation_type] == target_value:
                    matching_entries.append(entry)
        
        return matching_entries
    
    def get_value_statistics(
        self, 
        result: GematriaDictionaryResult, 
        calculation_type: str
    ) -> Dict[int, int]:
        """
        Get statistics about value distribution.
        
        Args:
            result: The dictionary result to analyze
            calculation_type: The calculation type to analyze
            
        Returns:
            Dictionary mapping values to their frequency
        """
        value_counts = {}
        
        for entry in result.entries:
            if calculation_type in entry.gematria_values:
                value = entry.gematria_values[calculation_type]
                value_counts[value] = value_counts.get(value, 0) + 1
        
        return value_counts
    
    def _analyze_verses(
        self, 
        text: str, 
        calc_types: Dict[Language, List[CalculationType]]
    ) -> List[VerseAnalysisEntry]:
        """
        Analyze individual verses and calculate their gematria sums.
        
        Args:
            text: The document text to analyze
            calc_types: Calculation types to use per language
            
        Returns:
            List of verse analysis entries
        """
        verse_entries = []
        lines = text.split('\n')
        current_verse_lines = []
        current_verse_number = None
        current_chapter_number = None
        
        for line in lines:
            # Skip empty lines
            if not line.strip():
                continue
            
            # Check if this is a chapter heading
            if self._is_chapter_heading(line.strip()):
                # Process previous verse if we have one
                if current_verse_number is not None and current_verse_lines:
                    verse_entry = self._process_verse(
                        current_verse_number, 
                        current_verse_lines, 
                        calc_types,
                        current_chapter_number
                    )
                    verse_entries.append(verse_entry)
                    current_verse_lines = []
                    current_verse_number = None
                
                # Update current chapter
                current_chapter_number = self._extract_chapter_number(line.strip())
                continue
                
            # Check if line starts with a verse number
            verse_match = re.match(r'^(\d+)[\.\s]', line.strip())
            if verse_match:
                # Process previous verse if we have one
                if current_verse_number is not None and current_verse_lines:
                    verse_entry = self._process_verse(
                        current_verse_number, 
                        current_verse_lines, 
                        calc_types,
                        current_chapter_number
                    )
                    verse_entries.append(verse_entry)
                
                # Start new verse
                current_verse_number = int(verse_match.group(1))
                current_verse_lines = [line]
            else:
                # This is a continuation line
                if current_verse_lines:
                    current_verse_lines.append(line)
        
        # Process the last verse
        if current_verse_number is not None and current_verse_lines:
            verse_entry = self._process_verse(
                current_verse_number, 
                current_verse_lines, 
                calc_types,
                current_chapter_number
            )
            verse_entries.append(verse_entry)
        
        return verse_entries
    
    def _process_verse(
        self, 
        verse_number: int, 
        verse_lines: List[str], 
        calc_types: Dict[Language, List[CalculationType]],
        chapter_number: Optional[int]
    ) -> VerseAnalysisEntry:
        """
        Process a single verse and calculate its gematria sums.
        
        Args:
            verse_number: The verse number
            verse_lines: Lines of text for this verse
            calc_types: Calculation types to use per language
            chapter_number: The chapter number for this verse
            
        Returns:
            VerseAnalysisEntry for this verse
        """
        # Combine all lines for this verse
        verse_text = ' '.join(verse_lines)
        
        # Remove verse number from the beginning for word extraction
        clean_text = re.sub(r'^\d+[\.\s]+', '', verse_text)
        
        # Extract words and numbers from the verse
        words_and_numbers = []
        for line in verse_lines:
            line_for_words = re.sub(r'^\d+[\.\s]+', '', line) if verse_number else line
            words_and_numbers.extend(self._extract_words_and_numbers_from_line(line_for_words))
        
        # Separate words from numbers
        words = []
        numbers = []
        for item in words_and_numbers:
            if item.isdigit():
                numbers.append(int(item))
            else:
                words.append(item)
        
        # Calculate numbers total
        numbers_total = sum(numbers)
        
        # Get all calculation types used in the document
        all_calc_types = []
        for lang_calc_types in calc_types.values():
            all_calc_types.extend(lang_calc_types)
        
        # Calculate gematria sums for words only (excluding numbers and ampersands)
        gematria_sums = {}
        verse_totals = {}
        
        for calc_type in all_calc_types:
            word_gematria_total = 0
            
            for word in words:
                try:
                    # Skip ampersands for gematria calculation (they have no value)
                    if word == '&':
                        continue
                        
                    normalized_word = self._normalize_word(word)
                    if normalized_word.strip():  # Skip empty words
                        value = self.gematria_service.calculate(normalized_word, calc_type)
                        word_gematria_total += value
                except Exception as e:
                    logger.warning(f"Failed to calculate {calc_type.display_name} for '{word}' in verse {verse_number}: {e}")
            
            gematria_sums[calc_type.display_name] = word_gematria_total
            verse_totals[calc_type.display_name] = word_gematria_total + numbers_total
        
        return VerseAnalysisEntry(
            verse_number=verse_number,
            verse_text=verse_text,
            word_count=len(words),
            number_count=len(numbers),
            gematria_sums=gematria_sums,
            numbers_total=numbers_total,
            verse_totals=verse_totals,
            chapter_number=chapter_number
        )
    
    def get_verse_breakdown(
        self,
        text: str,
        verse_number: int,
        custom_calculation_types: Optional[Dict[Language, List[CalculationType]]] = None
    ) -> Optional[VerseBreakdown]:
        """
        Get detailed breakdown of a specific verse.
        
        Args:
            text: The document text
            verse_number: The verse number to analyze
            custom_calculation_types: Custom calculation types per language
            
        Returns:
            VerseBreakdown for the specified verse, or None if not found
        """
        # Detect which languages are actually present in the document
        words_data = self._extract_words_with_verses(text)
        languages_present = set()
        for word, verse in words_data:
            # Skip numbers and ampersands for language detection
            if not word.isdigit() and word != '&':
                language = self._detect_word_language(self._normalize_word(word))
                languages_present.add(language)
        
        # Create calculation types only for languages actually present
        if custom_calculation_types:
            calc_types = custom_calculation_types
        else:
            calc_types = {}
            for language in languages_present:
                if language in self.default_calculation_types:
                    calc_types[language] = self.default_calculation_types[language]
                elif language == Language.UNKNOWN:
                    # For unknown words, use English TQ if available
                    calc_types[Language.ENGLISH] = self.default_calculation_types.get(Language.ENGLISH, [])
                    # Remove the UNKNOWN language to avoid duplicates
                    languages_present.discard(Language.UNKNOWN)
        
        # Find the verse in the text
        lines = text.split('\n')
        verse_lines = []
        current_verse_number = None
        found_verse = False
        
        for line in lines:
            # Skip empty lines
            if not line.strip():
                continue
                
            # Check if line starts with a verse number
            verse_match = re.match(r'^(\d+)[\.\s]', line.strip())
            if verse_match:
                current_verse_number = int(verse_match.group(1))
                if current_verse_number == verse_number:
                    found_verse = True
                    verse_lines = [line]
                elif found_verse:
                    # We've moved to the next verse, stop collecting
                    break
                else:
                    # Reset if we're not at the target verse yet
                    verse_lines = []
            else:
                # This is a continuation line
                if found_verse:
                    verse_lines.append(line)
        
        if not found_verse or not verse_lines:
            return None
        
        # Combine all lines for this verse
        verse_text = ' '.join(verse_lines)
        
        # Remove verse number from the beginning for word extraction
        clean_text = re.sub(r'^\d+[\.\s]+', '', verse_text)
        
        # Extract words and numbers from the verse
        words_and_numbers = []
        for line in verse_lines:
            line_for_words = re.sub(r'^\d+[\.\s]+', '', line)
            words_and_numbers.extend(self._extract_words_and_numbers_from_line(line_for_words))
        
        # Get all calculation types used
        all_calc_types = []
        for lang_calc_types in calc_types.values():
            all_calc_types.extend(lang_calc_types)
        
        calculation_types_used = [calc_type.display_name for calc_type in all_calc_types]
        
        # Create word entries
        word_entries = []
        total_gematria_sums = {calc_type: 0 for calc_type in calculation_types_used}
        
        for position, item in enumerate(words_and_numbers):
            # Check if item is a pure number
            is_number = item.isdigit()
            number_value = int(item) if is_number else None
            
            # Calculate gematria values
            gematria_values = {}
            
            if is_number:
                # For numbers, use face value for all calculation types
                for calc_type in calculation_types_used:
                    gematria_values[calc_type] = number_value
                    total_gematria_sums[calc_type] += number_value
            elif item == '&':
                # For ampersands, set gematria value to 0 for all calculation types
                for calc_type in calculation_types_used:
                    gematria_values[calc_type] = 0
                    # Don't add to total_gematria_sums since ampersands have no value
            else:
                # For words, calculate gematria
                normalized_word = self._normalize_word(item)
                for calc_type in all_calc_types:
                    try:
                        if normalized_word.strip():
                            value = self.gematria_service.calculate(normalized_word, calc_type)
                            gematria_values[calc_type.display_name] = value
                            total_gematria_sums[calc_type.display_name] += value
                    except Exception as e:
                        logger.warning(f"Failed to calculate {calc_type.display_name} for '{item}': {e}")
                        gematria_values[calc_type.display_name] = 0
            
            word_entry = VerseWordEntry(
                word=item,
                normalized_word=self._normalize_word(item) if not is_number else item,
                is_number=is_number,
                number_value=number_value,
                gematria_values=gematria_values,
                position_in_verse=position
            )
            word_entries.append(word_entry)
        
        return VerseBreakdown(
            verse_number=verse_number,
            verse_text=verse_text,
            words=word_entries,
            total_gematria_sums=total_gematria_sums,
            calculation_types_used=calculation_types_used
        )
    
    def _calculate_global_analysis(
        self, 
        verse_analysis: List[VerseAnalysisEntry]
    ) -> GlobalAnalysisSummary:
        """
        Calculate global analysis summary including triangular numbers.
        
        Args:
            verse_analysis: List of verse analysis entries
            
        Returns:
            GlobalAnalysisSummary with totals and global sums
        """
        if not verse_analysis:
            return GlobalAnalysisSummary(
                total_verses=0,
                triangular_number=0,
                total_gematria_sums={},
                global_sums={}
            )
        
        total_verses = len(verse_analysis)
        
        # Calculate triangular number (1 + 2 + 3 + ... + n)
        triangular_number = sum(range(1, total_verses + 1))
        
        # Sum up all gematria values by calculation type
        total_gematria_sums = {}
        
        # Get all calculation types from the first verse
        if verse_analysis:
            calc_types = list(verse_analysis[0].gematria_sums.keys())
            
            for calc_type in calc_types:
                total_sum = sum(
                    verse.gematria_sums.get(calc_type, 0) 
                    for verse in verse_analysis
                )
                total_gematria_sums[calc_type] = total_sum
        
        # Calculate global sums (total gematria + triangular number)
        global_sums = {
            calc_type: total_sum + triangular_number
            for calc_type, total_sum in total_gematria_sums.items()
        }
        
        return GlobalAnalysisSummary(
            total_verses=total_verses,
            triangular_number=triangular_number,
            total_gematria_sums=total_gematria_sums,
            global_sums=global_sums
        )
    
    def _create_chapter_summaries(
        self, 
        verse_analysis: List[VerseAnalysisEntry]
    ) -> List[ChapterSummary]:
        """
        Create chapter summaries from verse analysis data.
        
        Args:
            verse_analysis: List of verse analysis entries
            
        Returns:
            List of chapter summaries
        """
        # Group verses by chapter
        chapters = {}
        for verse in verse_analysis:
            chapter_num = verse.chapter_number
            if chapter_num is None:
                continue  # Skip verses without chapter information
                
            if chapter_num not in chapters:
                chapters[chapter_num] = []
            chapters[chapter_num].append(verse)
        
        # Create chapter summaries
        chapter_summaries = []
        for chapter_num in sorted(chapters.keys()):
            chapter_verses = chapters[chapter_num]
            
            # Calculate chapter statistics
            verse_count = len(chapter_verses)
            verse_numbers = [v.verse_number for v in chapter_verses]
            verse_range = f"{min(verse_numbers)}-{max(verse_numbers)}"
            total_words = sum(v.word_count for v in chapter_verses)
            
            # Calculate triangular number for this chapter
            triangular_number = (verse_count * (verse_count + 1)) // 2
            
            # Calculate gematria totals for the chapter
            gematria_totals = {}
            global_sums = {}
            
            # Get all calculation types from the first verse
            if chapter_verses:
                calc_types = list(chapter_verses[0].gematria_sums.keys())
                
                for calc_type in calc_types:
                    # Sum all verse gematria values for this calculation type
                    total_gematria = sum(
                        verse.gematria_sums.get(calc_type, 0) 
                        for verse in chapter_verses
                    )
                    gematria_totals[calc_type] = total_gematria
                    global_sums[calc_type] = total_gematria + triangular_number
            
            # Create chapter summary
            chapter_summary = ChapterSummary(
                chapter_number=chapter_num,
                chapter_title=f"Chapter {chapter_num}",
                verse_count=verse_count,
                verse_range=verse_range,
                total_words=total_words,
                gematria_totals=gematria_totals,
                triangular_number=triangular_number,
                global_sums=global_sums
            )
            chapter_summaries.append(chapter_summary)
        
        return chapter_summaries
    
    def _analyze_text_structure(self, text: str) -> TextStructureAnalysis:
        """
        Analyze the structure of the text to determine how it should be processed.
        
        Args:
            text: The document text to analyze
            
        Returns:
            TextStructureAnalysis with detected structure information
        """
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        total_lines = len(lines)
        
        if total_lines == 0:
            return TextStructureAnalysis(
                structure_type=TextStructureType.CONTINUOUS_TEXT,
                confidence=1.0,
                total_lines=0,
                analysis_notes=["Empty document"]
            )
        
        # Analyze numbering patterns
        numbered_lines = 0
        sequential_numbers = []
        chapter_divisions = False
        analysis_notes = []
        
        for i, line in enumerate(lines):
            # Check for chapter headings
            if self._is_chapter_heading(line):
                chapter_divisions = True
                continue
            
            # Check for leading numbers
            number_match = re.match(r'^(\d+)[\.\s]', line)
            if number_match:
                numbered_lines += 1
                sequential_numbers.append(int(number_match.group(1)))
        
        # Analyze sequential numbering
        sequential_numbering = False
        verse_like_pattern = False
        
        if sequential_numbers:
            # Sort the numbers to check for sequential pattern
            sorted_numbers = sorted(sequential_numbers)

            # Check if numbers are sequential starting from 1
            expected_sequence = list(range(1, len(sequential_numbers) + 1))
            if sequential_numbers == expected_sequence:
                sequential_numbering = True
                verse_like_pattern = True
                analysis_notes.append("Sequential numbering from 1 detected")
            elif sorted_numbers == expected_sequence:
                # Numbers are sequential but not in order in the text
                sequential_numbering = True
                verse_like_pattern = True
                analysis_notes.append("Sequential numbering from 1 detected (not in order)")
            elif len(set(sequential_numbers)) == len(sequential_numbers):
                # Unique numbers but check if they form a reasonable sequence
                if len(sequential_numbers) >= 3:  # Need at least 3 verses to be confident
                    # Check if it's a reasonable verse-like pattern
                    min_num = min(sequential_numbers)
                    max_num = max(sequential_numbers)
                    if min_num >= 1 and max_num - min_num + 1 == len(sequential_numbers):
                        sequential_numbering = True
                        verse_like_pattern = True
                        analysis_notes.append(f"Sequential numbering detected (starting from {min_num})")
                    else:
                        sequential_numbering = True
                        analysis_notes.append("Unique numbering detected (not sequential)")
                else:
                    # For small number of verses, be more lenient
                    sequential_numbering = True
                    verse_like_pattern = True
                    analysis_notes.append("Small verse-like numbering detected")
            else:
                analysis_notes.append("Non-sequential or duplicate numbering detected")
        
        # Determine structure type and confidence
        structure_type = TextStructureType.CONTINUOUS_TEXT
        confidence = 0.5
        
        if chapter_divisions and verse_like_pattern:
            structure_type = TextStructureType.VERSE_NUMBERED
            confidence = 0.9
            analysis_notes.append("Traditional verse structure with chapters detected")
        elif verse_like_pattern and not chapter_divisions:
            structure_type = TextStructureType.VERSE_NUMBERED
            confidence = 0.8
            analysis_notes.append("Verse-like numbering without chapters detected")
        elif numbered_lines > 0 and not verse_like_pattern:
            structure_type = TextStructureType.LINE_NUMBERED
            confidence = 0.7
            analysis_notes.append("Line numbering that doesn't follow verse pattern")
        elif numbered_lines == 0 and total_lines > 10:
            # Check for paragraph-like structure
            avg_line_length = sum(len(line) for line in lines) / len(lines)
            if avg_line_length > 50:  # Longer lines suggest paragraphs
                structure_type = TextStructureType.PARAGRAPH_BASED
                confidence = 0.6
                analysis_notes.append("Paragraph-based structure detected")
            else:
                structure_type = TextStructureType.CONTINUOUS_TEXT
                confidence = 0.6
                analysis_notes.append("Continuous text without clear structure")
        
        # Adjust confidence based on consistency
        numbering_ratio = numbered_lines / total_lines if total_lines > 0 else 0
        if numbering_ratio > 0.8:
            confidence = min(confidence + 0.1, 1.0)
        elif numbering_ratio < 0.3 and numbered_lines > 0:
            structure_type = TextStructureType.MIXED_STRUCTURE
            confidence = 0.4
            analysis_notes.append("Mixed structure with inconsistent numbering")

        # Special case: if we have any numbered lines that look like verses,
        # and the ratio is reasonable, treat as verse-numbered
        if (numbered_lines > 0 and numbering_ratio >= 0.5 and
            structure_type != TextStructureType.VERSE_NUMBERED):
            # Check if the numbered lines have reasonable content length
            avg_content_length = 0
            content_lines = 0
            for line in lines:
                if re.match(r'^(\d+)[\.\s]', line):
                    content = re.sub(r'^\d+[\.\s]+', '', line)
                    if content.strip():
                        avg_content_length += len(content.strip())
                        content_lines += 1

            if content_lines > 0:
                avg_content_length /= content_lines
                # If average content length suggests meaningful text (not just short labels)
                if avg_content_length > 20:  # Reasonable verse length
                    structure_type = TextStructureType.VERSE_NUMBERED
                    confidence = 0.7
                    analysis_notes.append("Verse-like structure detected based on content analysis")
        
        result = TextStructureAnalysis(
            structure_type=structure_type,
            confidence=confidence,
            total_lines=total_lines,
            numbered_lines=numbered_lines,
            sequential_numbering=sequential_numbering,
            verse_like_pattern=verse_like_pattern,
            chapter_divisions=chapter_divisions,
            analysis_notes=analysis_notes
        )

        # Debug logging
        logger.debug(f"Text structure analysis result: {structure_type.value}, confidence: {confidence:.2f}")
        logger.debug(f"Total lines: {total_lines}, numbered lines: {numbered_lines}")
        logger.debug(f"Sequential numbering: {sequential_numbering}, verse-like: {verse_like_pattern}")
        logger.debug(f"Analysis notes: {analysis_notes}")

        return result
    
    def _analyze_lines(
        self, 
        text: str, 
        calc_types: Dict[Language, List[CalculationType]],
        structure_analysis: TextStructureAnalysis
    ) -> List[LineAnalysisEntry]:
        """
        Perform line-by-line analysis for documents that don't follow verse structure.
        
        Args:
            text: The document text to analyze
            calc_types: Calculation types to use per language
            structure_analysis: The detected text structure
            
        Returns:
            List of line analysis entries
        """
        line_entries = []
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        for line_num, line in enumerate(lines, 1):
            # Skip chapter headings
            if self._is_chapter_heading(line):
                continue
            
            # Check for leading number
            has_leading_number = False
            leading_number = None
            line_for_analysis = line
            
            number_match = re.match(r'^(\d+)[\.\s]', line)
            if number_match:
                has_leading_number = True
                leading_number = int(number_match.group(1))
                # Remove the leading number for word analysis
                line_for_analysis = re.sub(r'^\d+[\.\s]+', '', line)
            
            # Determine if this line is likely a verse based on structure analysis
            is_likely_verse = (
                structure_analysis.structure_type == TextStructureType.VERSE_NUMBERED and
                has_leading_number and
                structure_analysis.verse_like_pattern
            )
            
            # Extract words and numbers from the line
            words_and_numbers = self._extract_words_and_numbers_from_line(line_for_analysis)
            
            # Separate words from numbers
            words = []
            numbers = []
            for item in words_and_numbers:
                if item.isdigit():
                    numbers.append(int(item))
                else:
                    words.append(item)
            
            # Calculate numbers total
            numbers_total = sum(numbers)
            
            # Get all calculation types used in the document
            all_calc_types = []
            for lang_calc_types in calc_types.values():
                all_calc_types.extend(lang_calc_types)
            
            # Calculate gematria sums for words only
            gematria_sums = {}
            line_totals = {}
            
            for calc_type in all_calc_types:
                word_gematria_total = 0
                
                for word in words:
                    try:
                        # Skip ampersands for gematria calculation
                        if word == '&':
                            continue
                            
                        normalized_word = self._normalize_word(word)
                        if normalized_word.strip():
                            value = self.gematria_service.calculate(normalized_word, calc_type)
                            word_gematria_total += value
                    except Exception as e:
                        logger.warning(f"Failed to calculate {calc_type.display_name} for '{word}' in line {line_num}: {e}")
                
                gematria_sums[calc_type.display_name] = word_gematria_total
                line_totals[calc_type.display_name] = word_gematria_total + numbers_total
            
            line_entry = LineAnalysisEntry(
                line_number=line_num,
                line_text=line,
                word_count=len(words),
                number_count=len(numbers),
                gematria_sums=gematria_sums,
                numbers_total=numbers_total,
                line_totals=line_totals,
                has_leading_number=has_leading_number,
                leading_number=leading_number,
                is_likely_verse=is_likely_verse
            )
            line_entries.append(line_entry)
        
        return line_entries
    
    def _calculate_global_analysis_from_lines(
        self, 
        line_analysis: List[LineAnalysisEntry]
    ) -> GlobalAnalysisSummary:
        """
        Calculate global analysis summary from line analysis data.
        
        Args:
            line_analysis: List of line analysis entries
            
        Returns:
            GlobalAnalysisSummary with totals and global sums
        """
        if not line_analysis:
            return GlobalAnalysisSummary(
                total_verses=0,
                triangular_number=0,
                total_gematria_sums={},
                global_sums={}
            )
        
        total_verses = len(line_analysis)
        
        # Calculate triangular number (1 + 2 + 3 + ... + n)
        triangular_number = sum(range(1, total_verses + 1))
        
        # Sum up all gematria values by calculation type
        total_gematria_sums = {}
        
        # Get all calculation types from the first line
        if line_analysis:
            calc_types = list(line_analysis[0].gematria_sums.keys())
            
            for calc_type in calc_types:
                total_sum = sum(
                    line.gematria_sums.get(calc_type, 0) 
                    for line in line_analysis
                )
                total_gematria_sums[calc_type] = total_sum
        
        # Calculate global sums (total gematria + triangular number)
        global_sums = {
            calc_type: total_sum + triangular_number
            for calc_type, total_sum in total_gematria_sums.items()
        }
        
        return GlobalAnalysisSummary(
            total_verses=total_verses,
            triangular_number=triangular_number,
            total_gematria_sums=total_gematria_sums,
            global_sums=global_sums
        ) 