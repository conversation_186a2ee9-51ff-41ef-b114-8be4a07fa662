"""
Document State Manager

Provides centralized state management and coordination between
Document Browser, Advanced Search, and Knowledge Base components.
"""

from typing import Optional, Dict, List, Any, Callable
from PyQt6.QtCore import QObject, pyqtSignal
from dataclasses import dataclass, field
from datetime import datetime

from loguru import logger
from document_manager.models.document import Document


@dataclass
class DocumentContext:
    """Context information for document operations."""
    current_document_id: Optional[str] = None
    current_document: Optional[Document] = None
    selected_document_ids: List[str] = field(default_factory=list)
    last_search_query: Optional[str] = None
    last_search_results: List[Document] = field(default_factory=list)
    current_category: Optional[str] = None
    current_filters: Dict[str, Any] = field(default_factory=dict)
    navigation_history: List[str] = field(default_factory=list)
    component_states: Dict[str, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class SearchContext:
    """Context information for search operations."""
    query: str = ""
    filters: Dict[str, Any] = field(default_factory=dict)
    results: List[Document] = field(default_factory=list)
    total_matches: int = 0
    search_time: Optional[datetime] = None
    component_source: str = ""


class DocumentStateManager(QObject):
    """Centralized state manager for document management components."""
    
    # Signals for cross-component communication
    document_selected = pyqtSignal(str, str)  # document_id, source_component
    document_opened = pyqtSignal(str, str)    # document_id, source_component
    search_performed = pyqtSignal(object)     # SearchContext
    navigation_requested = pyqtSignal(str, str, dict)  # target_component, action, params
    state_changed = pyqtSignal(str, dict)     # component_name, state_data
    
    def __init__(self):
        """Initialize the state manager."""
        super().__init__()
        
        # Current context
        self.document_context = DocumentContext()
        self.search_context = SearchContext()
        
        # Component registry
        self.registered_components: Dict[str, QObject] = {}
        self.component_callbacks: Dict[str, Dict[str, Callable]] = {}
        
        logger.debug("DocumentStateManager initialized")
    
    def register_component(self, component_name: str, component: QObject, 
                          callbacks: Optional[Dict[str, Callable]] = None):
        """Register a component with the state manager.
        
        Args:
            component_name: Unique name for the component
            component: The component object
            callbacks: Optional callbacks for component-specific actions
        """
        self.registered_components[component_name] = component
        if callbacks:
            self.component_callbacks[component_name] = callbacks
        
        # Initialize component state
        self.document_context.component_states[component_name] = {}
        
        logger.debug(f"Registered component: {component_name}")
    
    def unregister_component(self, component_name: str):
        """Unregister a component from the state manager."""
        if component_name in self.registered_components:
            del self.registered_components[component_name]
        if component_name in self.component_callbacks:
            del self.component_callbacks[component_name]
        if component_name in self.document_context.component_states:
            del self.document_context.component_states[component_name]
        
        logger.debug(f"Unregistered component: {component_name}")
    
    def set_current_document(self, document_id: str, document: Optional[Document] = None, 
                           source_component: str = ""):
        """Set the current document and notify all components.
        
        Args:
            document_id: ID of the selected document
            document: Optional Document object
            source_component: Component that initiated the selection
        """
        # Update context
        self.document_context.current_document_id = document_id
        self.document_context.current_document = document
        
        # Add to navigation history
        if document_id not in self.document_context.navigation_history:
            self.document_context.navigation_history.append(document_id)
            # Keep history to reasonable size
            if len(self.document_context.navigation_history) > 50:
                self.document_context.navigation_history.pop(0)
        
        # Emit signal
        self.document_selected.emit(document_id, source_component)
        
        logger.debug(f"Current document set to {document_id} from {source_component}")
    
    def set_selected_documents(self, document_ids: List[str], source_component: str = ""):
        """Set multiple selected documents.
        
        Args:
            document_ids: List of selected document IDs
            source_component: Component that initiated the selection
        """
        self.document_context.selected_document_ids = document_ids
        
        # If single selection, also set as current
        if len(document_ids) == 1:
            self.set_current_document(document_ids[0], source_component=source_component)
        
        logger.debug(f"Selected documents: {len(document_ids)} from {source_component}")
    
    def perform_search(self, query: str, filters: Optional[Dict[str, Any]] = None,
                      source_component: str = ""):
        """Perform a search and update search context.
        
        Args:
            query: Search query
            filters: Optional search filters
            source_component: Component that initiated the search
        """
        # Update search context
        self.search_context.query = query
        self.search_context.filters = filters or {}
        self.search_context.search_time = datetime.now()
        self.search_context.component_source = source_component
        
        # Update document context
        self.document_context.last_search_query = query
        
        # Emit signal
        self.search_performed.emit(self.search_context)
        
        logger.debug(f"Search performed: '{query}' from {source_component}")
    
    def update_search_results(self, results: List[Document], total_matches: int = 0):
        """Update search results in the context.
        
        Args:
            results: List of search result documents
            total_matches: Total number of matches found
        """
        self.search_context.results = results
        self.search_context.total_matches = total_matches
        self.document_context.last_search_results = results
        
        logger.debug(f"Search results updated: {len(results)} documents, {total_matches} matches")
    
    def navigate_to_component(self, target_component: str, action: str, 
                            params: Optional[Dict[str, Any]] = None):
        """Request navigation to another component.
        
        Args:
            target_component: Name of the target component
            action: Action to perform in the target component
            params: Optional parameters for the action
        """
        params = params or {}
        
        # Add current context to parameters
        if self.document_context.current_document_id:
            params['document_id'] = self.document_context.current_document_id
        if self.search_context.query:
            params['search_query'] = self.search_context.query
        
        # Emit navigation signal
        self.navigation_requested.emit(target_component, action, params)
        
        logger.debug(f"Navigation requested: {target_component}.{action} with {len(params)} params")
    
    def update_component_state(self, component_name: str, state_data: Dict[str, Any]):
        """Update state for a specific component.
        
        Args:
            component_name: Name of the component
            state_data: State data to update
        """
        if component_name not in self.document_context.component_states:
            self.document_context.component_states[component_name] = {}
        
        self.document_context.component_states[component_name].update(state_data)
        
        # Emit state change signal
        self.state_changed.emit(component_name, state_data)
        
        logger.debug(f"Component state updated: {component_name}")
    
    def get_component_state(self, component_name: str) -> Dict[str, Any]:
        """Get state for a specific component.
        
        Args:
            component_name: Name of the component
            
        Returns:
            Component state data
        """
        return self.document_context.component_states.get(component_name, {})
    
    def get_current_context(self) -> Dict[str, Any]:
        """Get the current complete context.
        
        Returns:
            Dictionary containing all context information
        """
        return {
            'document': {
                'current_id': self.document_context.current_document_id,
                'selected_ids': self.document_context.selected_document_ids,
                'category': self.document_context.current_category,
                'filters': self.document_context.current_filters,
                'history': self.document_context.navigation_history[-10:]  # Last 10
            },
            'search': {
                'query': self.search_context.query,
                'filters': self.search_context.filters,
                'results_count': len(self.search_context.results),
                'total_matches': self.search_context.total_matches,
                'source': self.search_context.component_source
            },
            'components': list(self.registered_components.keys())
        }
    
    def clear_context(self):
        """Clear all context data."""
        self.document_context = DocumentContext()
        self.search_context = SearchContext()
        
        logger.debug("Context cleared")


# Global state manager instance
_state_manager = None

def get_state_manager() -> DocumentStateManager:
    """Get the global document state manager instance."""
    global _state_manager
    if _state_manager is None:
        _state_manager = DocumentStateManager()
    return _state_manager
