"""
Unified Analysis Dialog - Consolidates all document analysis functionality.

This dialog combines the functionality of:
- GematriaDictionaryDialog: Full document gematria analysis
- VerseBreakdownDialog: Individual verse analysis
- ConcordanceCreationDialog: KWIC concordance creation

Key features:
- Tabbed interface for different analysis types
- Unified text input and document loading
- Comprehensive gematria analysis with export options
- Verse-by-verse breakdown capabilities
- KWIC concordance generation
- Integrated results display and export

Author: Assistant
Created: 2024-12-28
Dependencies: PyQt6, pathlib, typing, loguru
"""

import csv
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSortFilterProxyModel
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QFont
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QGroupBox, QFormLayout, QLineEdit, QTextEdit, QComboBox,
    QCheckBox, QSpinBox, QPushButton, QProgressBar, QLabel,
    QListWidget, QListWidgetItem, QTableView, QFileDialog,
    QMessageBox, QDialogButtonBox, QSplitter, QHeaderView,
    QTreeWidget, QTreeWidgetItem
)

from loguru import logger

from gematria.models.calculation_type import CalculationType, Language
from gematria.services.gematria_service import GematriaService
from document_manager.models.document import Document
from document_manager.models.gematria_dictionary_entry import (
    GematriaDictionaryEntry, GematriaDictionaryResult, VerseAnalysisEntry,
    GlobalAnalysisSummary, VerseBreakdown
)
from document_manager.services.document_service import DocumentService
from document_manager.services.category_service import CategoryService
from document_manager.services.gematria_dictionary_service import GematriaDictionaryService
from document_manager.services.concordance_service import ConcordanceService
from shared.ui.components.message_box import MessageBox
from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


class AnalysisWorker(QThread):
    """Worker thread for analysis operations."""
    
    progress_updated = pyqtSignal(int, int, str)  # current, total, status
    analysis_completed = pyqtSignal(dict)  # Results dictionary
    error_occurred = pyqtSignal(str)  # Error message
    
    def __init__(self, analysis_config: Dict[str, Any]):
        """Initialize the analysis worker.
        
        Args:
            analysis_config: Configuration dictionary with analysis parameters
        """
        super().__init__()
        self.analysis_config = analysis_config
        self._stop_requested = False
        
        # Initialize services
        self.gematria_service = GematriaService()
        self.dictionary_service = GematriaDictionaryService()
        self.concordance_service = ConcordanceService()
    
    def run(self):
        """Run the analysis process based on configuration."""
        try:
            analysis_type = self.analysis_config.get('type', 'gematria')
            
            if analysis_type == 'gematria':
                self._run_gematria_analysis()
            elif analysis_type == 'verse_breakdown':
                self._run_verse_breakdown()
            elif analysis_type == 'concordance':
                self._run_concordance_creation()
            else:
                self.error_occurred.emit(f"Unknown analysis type: {analysis_type}")
                
        except Exception as e:
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def _run_gematria_analysis(self):
        """Run gematria dictionary analysis."""
        text = self.analysis_config.get('text', '')
        title = self.analysis_config.get('title', 'Untitled Document')
        
        self.progress_updated.emit(0, 0, "Starting gematria analysis...")
        
        try:
            result = self.dictionary_service.analyze_document(text=text, document_title=title)
            
            if result and not self._stop_requested:
                formatted_result = {
                    'type': 'gematria',
                    'result': result,
                    'text': text,
                    'title': title
                }
                self.analysis_completed.emit(formatted_result)
            elif not self._stop_requested:
                self.error_occurred.emit("Analysis failed to produce results")
                
        except Exception as e:
            logger.error(f"Error during gematria analysis: {e}")
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def _run_verse_breakdown(self):
        """Run verse breakdown analysis."""
        text = self.analysis_config.get('text', '')
        verse_number = self.analysis_config.get('verse_number', 1)
        
        self.progress_updated.emit(0, 0, f"Analyzing verse {verse_number}...")
        
        try:
            # Create a verse breakdown using the dictionary service
            breakdown = self.dictionary_service.create_verse_breakdown(text, verse_number)
            
            if breakdown and not self._stop_requested:
                formatted_result = {
                    'type': 'verse_breakdown',
                    'breakdown': breakdown,
                    'verse_number': verse_number
                }
                self.analysis_completed.emit(formatted_result)
            elif not self._stop_requested:
                self.error_occurred.emit("Verse breakdown failed")
                
        except Exception as e:
            logger.error(f"Error during verse breakdown: {e}")
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def _run_concordance_creation(self):
        """Run concordance creation."""
        config = self.analysis_config
        
        self.progress_updated.emit(0, 0, "Creating concordance...")
        
        try:
            concordance_table = self.concordance_service.generate_concordance(
                name=config.get('name', 'Untitled Concordance'),
                keywords=config.get('keywords', []),
                document_ids=config.get('document_ids', []),
                settings=config.get('settings', {}),
                description=config.get('description'),
                tags=config.get('tags', []),
                created_by=config.get('created_by')
            )
            
            if concordance_table and not self._stop_requested:
                formatted_result = {
                    'type': 'concordance',
                    'concordance_table': concordance_table
                }
                self.analysis_completed.emit(formatted_result)
            elif not self._stop_requested:
                self.error_occurred.emit("Concordance creation failed")
                
        except Exception as e:
            logger.error(f"Error during concordance creation: {e}")
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def stop(self):
        """Request stopping the analysis process."""
        self._stop_requested = True


class UnifiedAnalysisDialog(QDialog):
    """Unified dialog for all document analysis operations."""
    
    # Signals
    analysis_completed = pyqtSignal(dict)  # Analysis results
    word_selected = pyqtSignal(str, dict)  # word, gematria_values
    verse_breakdown_requested = pyqtSignal(object)  # VerseBreakdown object
    
    def __init__(self, parent=None, initial_text: str = "", initial_title: str = ""):
        """Initialize the unified analysis dialog.
        
        Args:
            parent: Parent widget
            initial_text: Initial text to analyze
            initial_title: Initial document title
        """
        super().__init__(parent)
        
        self.setWindowTitle("Document Analysis")
        self.setModal(True)
        self.resize(1200, 800)
        
        # Services
        self.document_service = DocumentService()
        self.category_service = CategoryService()
        self.gematria_service = GematriaService()
        self.dictionary_service = GematriaDictionaryService()
        self.concordance_service = ConcordanceService()
        
        # State
        self.analysis_worker: Optional[AnalysisWorker] = None
        self.current_gematria_result: Optional[GematriaDictionaryResult] = None
        self.current_verse_breakdown: Optional[VerseBreakdown] = None
        self.current_text: str = initial_text
        
        # Data models
        self.word_table_model = QStandardItemModel()
        self.word_proxy_model = QSortFilterProxyModel()
        self.verse_table_model = QStandardItemModel()
        
        # Initialize UI
        self._init_ui()
        self._setup_models()
        self._connect_signals()
        self._load_initial_data()
        
        # Set initial text if provided
        if initial_text:
            self.text_input.setPlainText(initial_text)
        if initial_title:
            self.title_input.setText(initial_title)
        
        logger.debug("UnifiedAnalysisDialog initialized")
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_gematria_analysis_tab()
        self._create_verse_breakdown_tab()
        self._create_concordance_tab()
        self._create_results_tab()
        
        # Button box
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Close
        )
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def _create_gematria_analysis_tab(self):
        """Create the gematria analysis tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Input section
        input_group = QGroupBox("Document Input")
        input_layout = QFormLayout(input_group)
        
        # Title input
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Enter document title")
        input_layout.addRow("Title:", self.title_input)
        
        # Text input
        self.text_input = UnicodeTextEdit()
        self.text_input.setPlaceholderText(
            "Enter or paste text to analyze...\n\n"
            "Features:\n"
            "• Comprehensive gematria analysis\n"
            "• Word frequency and verse mapping\n"
            "• Multiple calculation methods\n"
            "• Export results in various formats"
        )
        self.text_input.setMaximumHeight(200)
        input_layout.addRow("Text:", self.text_input)
        
        # Import text button
        import_layout = QHBoxLayout()
        self.import_text_btn = QPushButton("Import Text File")
        self.import_text_btn.clicked.connect(self._import_text_file)
        import_layout.addWidget(self.import_text_btn)
        
        self.load_document_btn = QPushButton("Load Document")
        self.load_document_btn.clicked.connect(self._load_document)
        import_layout.addWidget(self.load_document_btn)
        
        import_layout.addStretch()
        input_layout.addRow("", import_layout)
        
        layout.addWidget(input_group)
        
        # Analysis options
        options_group = QGroupBox("Analysis Options")
        options_layout = QFormLayout(options_group)
        
        # Calculation method
        self.calc_method_combo = QComboBox()
        self._populate_calculation_methods()
        options_layout.addRow("Calculation Method:", self.calc_method_combo)
        
        # Language filter
        self.language_combo = QComboBox()
        self.language_combo.addItem("All Languages", None)
        for lang in Language:
            self.language_combo.addItem(lang.value, lang)
        options_layout.addRow("Language Filter:", self.language_combo)
        
        layout.addWidget(options_group)
        
        # Analysis button
        self.analyze_btn = QPushButton("Analyze Document")
        self.analyze_btn.clicked.connect(self._start_gematria_analysis)
        layout.addWidget(self.analyze_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Gematria Analysis")
    
    def _create_verse_breakdown_tab(self):
        """Create the verse breakdown tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Description
        desc_label = QLabel(
            "Analyze individual verses with detailed word-by-word gematria breakdown."
        )
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Verse input section
        verse_group = QGroupBox("Verse Input")
        verse_layout = QFormLayout(verse_group)
        
        # Verse number
        self.verse_number_spin = QSpinBox()
        self.verse_number_spin.setMinimum(1)
        self.verse_number_spin.setMaximum(9999)
        self.verse_number_spin.setValue(1)
        verse_layout.addRow("Verse Number:", self.verse_number_spin)
        
        # Verse text
        self.verse_text_input = UnicodeTextEdit()
        self.verse_text_input.setPlaceholderText("Enter verse text for detailed analysis...")
        self.verse_text_input.setMaximumHeight(100)
        verse_layout.addRow("Verse Text:", self.verse_text_input)
        
        layout.addWidget(verse_group)
        
        # Analysis button
        self.verse_analyze_btn = QPushButton("Analyze Verse")
        self.verse_analyze_btn.clicked.connect(self._start_verse_breakdown)
        layout.addWidget(self.verse_analyze_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Verse Breakdown")
    
    def _create_concordance_tab(self):
        """Create the concordance creation tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Description
        desc_label = QLabel(
            "Create KWIC (Key Word In Context) concordances for detailed text analysis."
        )
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Basic info section
        info_group = QGroupBox("Concordance Information")
        info_layout = QFormLayout(info_group)
        
        # Name
        self.concordance_name_input = QLineEdit()
        self.concordance_name_input.setPlaceholderText("Enter concordance name")
        info_layout.addRow("Name:", self.concordance_name_input)
        
        # Description
        self.concordance_desc_input = QTextEdit()
        self.concordance_desc_input.setMaximumHeight(60)
        self.concordance_desc_input.setPlaceholderText("Optional description")
        info_layout.addRow("Description:", self.concordance_desc_input)
        
        # Keywords
        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("Enter keywords separated by commas")
        info_layout.addRow("Keywords:", self.keywords_input)
        
        layout.addWidget(info_group)
        
        # Settings section
        settings_group = QGroupBox("Concordance Settings")
        settings_layout = QFormLayout(settings_group)
        
        # Context window
        self.context_window_spin = QSpinBox()
        self.context_window_spin.setRange(10, 500)
        self.context_window_spin.setValue(50)
        self.context_window_spin.setSuffix(" characters")
        settings_layout.addRow("Context window:", self.context_window_spin)
        
        # Case sensitive
        self.case_sensitive_check = QCheckBox("Case sensitive matching")
        settings_layout.addRow("", self.case_sensitive_check)
        
        # Include punctuation
        self.include_punctuation_check = QCheckBox("Include punctuation in context")
        self.include_punctuation_check.setChecked(True)
        settings_layout.addRow("", self.include_punctuation_check)
        
        layout.addWidget(settings_group)
        
        # Create button
        self.create_concordance_btn = QPushButton("Create Concordance")
        self.create_concordance_btn.clicked.connect(self._start_concordance_creation)
        layout.addWidget(self.create_concordance_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Concordance Creation")

    def _create_results_tab(self):
        """Create the results display tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Progress section
        progress_group = QGroupBox("Analysis Progress")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_label = QLabel("Ready to analyze")
        progress_layout.addWidget(self.progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # Cancel button
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self._cancel_analysis)
        progress_layout.addWidget(self.cancel_btn)

        layout.addWidget(progress_group)

        # Results section with splitter
        results_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Results tree/table
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Results header with export button
        results_header = QHBoxLayout()
        results_label = QLabel("Analysis Results")
        results_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        results_header.addWidget(results_label)

        results_header.addStretch()

        self.export_results_btn = QPushButton("Export Results")
        self.export_results_btn.setEnabled(False)
        self.export_results_btn.clicked.connect(self._export_results)
        results_header.addWidget(self.export_results_btn)

        left_layout.addLayout(results_header)

        # Results table
        self.results_table = QTableView()
        self.results_table.setSortingEnabled(True)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        left_layout.addWidget(self.results_table)

        # Right side - Details panel
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Details header
        details_label = QLabel("Analysis Details")
        details_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        right_layout.addWidget(details_label)

        # Details text area
        self.details_text = UnicodeTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setPlaceholderText("Select an item from the results to view details...")
        right_layout.addWidget(self.details_text)

        # Add widgets to splitter
        results_splitter.addWidget(left_widget)
        results_splitter.addWidget(right_widget)
        results_splitter.setSizes([600, 400])

        layout.addWidget(results_splitter)

        self.tab_widget.addTab(tab, "Results")

    def _setup_models(self):
        """Set up data models for tables."""
        # Set up word table proxy model
        self.word_proxy_model.setSourceModel(self.word_table_model)
        self.results_table.setModel(self.word_proxy_model)

    def _connect_signals(self):
        """Connect UI signals to their handlers."""
        # Results table selection
        self.results_table.selectionModel().selectionChanged.connect(self._on_result_selection_changed)

        # Text input changes
        self.text_input.textChanged.connect(self._on_text_changed)
        self.verse_text_input.textChanged.connect(self._on_verse_text_changed)

    def _load_initial_data(self):
        """Load initial data for combo boxes."""
        self._populate_calculation_methods()

    def _populate_calculation_methods(self):
        """Populate the calculation methods combo box."""
        self.calc_method_combo.clear()

        # Add available calculation types
        for calc_type in CalculationType:
            self.calc_method_combo.addItem(calc_type.name, calc_type)

    def _on_text_changed(self):
        """Handle text input changes."""
        has_text = bool(self.text_input.toPlainText().strip())
        self.analyze_btn.setEnabled(has_text)

    def _on_verse_text_changed(self):
        """Handle verse text input changes."""
        has_text = bool(self.verse_text_input.toPlainText().strip())
        self.verse_analyze_btn.setEnabled(has_text)

    def _import_text_file(self):
        """Import text from a file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Text File", "",
            "Text Files (*.txt *.md *.rtf);;All Files (*)"
        )

        if file_path:
            try:
                # Try different encodings
                encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                content = None

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as file:
                            content = file.read()
                        logger.debug(f"Successfully read file with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue

                if content is None:
                    # Last resort: read as binary and decode with error handling
                    with open(file_path, 'rb') as file:
                        raw_content = file.read()
                    content = raw_content.decode('utf-8', errors='replace')
                    logger.warning(f"Used fallback encoding with error replacement for {file_path}")

                self.text_input.setPlainText(content)

                # Set title from filename
                filename = Path(file_path).stem
                if not self.title_input.text().strip():
                    self.title_input.setText(filename)

                MessageBox.information(
                    self, "Import Complete",
                    f"Text imported from {Path(file_path).name}"
                )

            except Exception as e:
                logger.error(f"Error importing text file: {e}")
                MessageBox.error(self, "Error", f"Failed to import text file: {str(e)}")

    def _load_document(self):
        """Load a document from the database."""
        # Get available documents
        documents = self.document_service.get_all_documents()

        if not documents:
            MessageBox.information(self, "No Documents", "No documents available in the database.")
            return

        # Create selection dialog
        from PyQt6.QtWidgets import QInputDialog

        document_names = [f"{doc.name} ({doc.file_type.value})" for doc in documents]

        selected_name, ok = QInputDialog.getItem(
            self, "Select Document", "Choose a document to analyze:",
            document_names, 0, False
        )

        if ok and selected_name:
            # Find the selected document
            selected_index = document_names.index(selected_name)
            selected_document = documents[selected_index]

            # Load document content
            content = selected_document.extracted_text or selected_document.content or ""
            if not content and selected_document.file_path:
                # Try to extract text if not already done
                self.document_service.extract_text(selected_document)
                content = selected_document.extracted_text or ""

            if content:
                self.text_input.setPlainText(content)
                self.title_input.setText(selected_document.name)

                MessageBox.information(
                    self, "Document Loaded",
                    f"Loaded document: {selected_document.name}"
                )
            else:
                MessageBox.warning(
                    self, "No Content",
                    f"No text content available for document: {selected_document.name}"
                )

    def _start_gematria_analysis(self):
        """Start gematria analysis."""
        text = self.text_input.toPlainText().strip()
        if not text:
            MessageBox.warning(self, "No Text", "Please enter text to analyze.")
            return

        title = self.title_input.text().strip() or "Untitled Document"

        analysis_config = {
            'type': 'gematria',
            'text': text,
            'title': title
        }

        self._start_analysis_worker(analysis_config)

    def _start_verse_breakdown(self):
        """Start verse breakdown analysis."""
        text = self.verse_text_input.toPlainText().strip()
        if not text:
            MessageBox.warning(self, "No Text", "Please enter verse text to analyze.")
            return

        verse_number = self.verse_number_spin.value()

        analysis_config = {
            'type': 'verse_breakdown',
            'text': text,
            'verse_number': verse_number
        }

        self._start_analysis_worker(analysis_config)

    def _start_concordance_creation(self):
        """Start concordance creation."""
        name = self.concordance_name_input.text().strip()
        if not name:
            MessageBox.warning(self, "No Name", "Please enter a name for the concordance.")
            return

        keywords_text = self.keywords_input.text().strip()
        if not keywords_text:
            MessageBox.warning(self, "No Keywords", "Please enter keywords for the concordance.")
            return

        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

        # Get text from gematria tab if available
        text = self.text_input.toPlainText().strip()
        if not text:
            MessageBox.warning(self, "No Text", "Please enter text in the Gematria Analysis tab first.")
            return

        # Create a temporary document for concordance
        # In a real implementation, you'd want to select from existing documents
        temp_doc_id = "temp_analysis_doc"

        analysis_config = {
            'type': 'concordance',
            'name': name,
            'description': self.concordance_desc_input.toPlainText().strip() or None,
            'keywords': keywords,
            'document_ids': [temp_doc_id],  # Would be real document IDs
            'settings': {
                'context_window': self.context_window_spin.value(),
                'case_sensitive': self.case_sensitive_check.isChecked(),
                'include_punctuation': self.include_punctuation_check.isChecked()
            }
        }

        self._start_analysis_worker(analysis_config)

    def _start_analysis_worker(self, analysis_config: Dict[str, Any]):
        """Start the analysis worker with the given configuration."""
        # Switch to results tab
        self.tab_widget.setCurrentIndex(3)

        # Update UI for operation
        self._disable_controls()
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate initially
        self.cancel_btn.setEnabled(True)
        self._clear_results()

        # Start worker
        self.analysis_worker = AnalysisWorker(analysis_config)
        self.analysis_worker.progress_updated.connect(self._on_progress_updated)
        self.analysis_worker.analysis_completed.connect(self._on_analysis_completed)
        self.analysis_worker.error_occurred.connect(self._on_analysis_error)
        self.analysis_worker.start()

        analysis_type = analysis_config.get('type', 'analysis')
        self.progress_label.setText(f"Starting {analysis_type}...")

    def _disable_controls(self):
        """Disable all analysis controls during operation."""
        self.analyze_btn.setEnabled(False)
        self.verse_analyze_btn.setEnabled(False)
        self.create_concordance_btn.setEnabled(False)
        self.import_text_btn.setEnabled(False)
        self.load_document_btn.setEnabled(False)

    def _enable_controls(self):
        """Re-enable all analysis controls after operation."""
        self.import_text_btn.setEnabled(True)
        self.load_document_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)

        # Update button states based on current text
        self._on_text_changed()
        self._on_verse_text_changed()

        # Enable concordance button if name and keywords are provided
        has_name = bool(self.concordance_name_input.text().strip())
        has_keywords = bool(self.keywords_input.text().strip())
        self.create_concordance_btn.setEnabled(has_name and has_keywords)

    def _cancel_analysis(self):
        """Cancel the current analysis operation."""
        if self.analysis_worker and self.analysis_worker.isRunning():
            self.analysis_worker.stop()
            self.analysis_worker.wait(3000)  # Wait up to 3 seconds

        self._enable_controls()
        self.progress_bar.setVisible(False)
        self.progress_label.setText("Analysis cancelled")

        MessageBox.information(self, "Cancelled", "Analysis operation was cancelled.")

    def _clear_results(self):
        """Clear all results displays."""
        self.word_table_model.clear()
        self.details_text.clear()
        self.export_results_btn.setEnabled(False)

    def _on_progress_updated(self, current: int, total: int, status: str):
        """Handle progress updates from the worker."""
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
            self.progress_label.setText(f"{status} ({current}/{total})")
        else:
            self.progress_label.setText(status)

    def _on_analysis_completed(self, results: Dict[str, Any]):
        """Handle analysis completion."""
        self._enable_controls()
        self.progress_bar.setVisible(False)

        analysis_type = results.get('type', 'analysis')

        if analysis_type == 'gematria':
            self._handle_gematria_results(results)
        elif analysis_type == 'verse_breakdown':
            self._handle_verse_breakdown_results(results)
        elif analysis_type == 'concordance':
            self._handle_concordance_results(results)

        # Enable export button
        self.export_results_btn.setEnabled(True)

        # Emit completion signal
        self.analysis_completed.emit(results)

    def _handle_gematria_results(self, results: Dict[str, Any]):
        """Handle gematria analysis results."""
        gematria_result = results.get('result')
        if not gematria_result:
            return

        self.current_gematria_result = gematria_result

        self.progress_label.setText(
            f"Gematria analysis complete: {gematria_result.unique_words} unique words analyzed"
        )

        # Set up table headers
        headers = ["Word", "Language", "Frequency", "Verses"]

        # Add gematria value columns
        for calc_type in sorted(gematria_result.calculation_types_used):
            headers.append(f"Gematria ({calc_type})")

        self.word_table_model.setHorizontalHeaderLabels(headers)

        # Populate table data
        for entry in gematria_result.entries:
            row_items = []

            # Basic info
            row_items.append(QStandardItem(entry.word))
            row_items.append(QStandardItem(entry.language.value))
            row_items.append(QStandardItem(str(entry.frequency)))

            # Verses
            verses_text = ", ".join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else "N/A"
            row_items.append(QStandardItem(verses_text))

            # Gematria values
            for calc_type in sorted(gematria_result.calculation_types_used):
                value = entry.gematria_values.get(calc_type, "")
                row_items.append(QStandardItem(str(value) if value else ""))

            self.word_table_model.appendRow(row_items)

        # Resize columns
        self.results_table.resizeColumnsToContents()

        # Update details with global analysis
        if gematria_result.global_analysis:
            self._display_global_analysis(gematria_result.global_analysis)

    def _handle_verse_breakdown_results(self, results: Dict[str, Any]):
        """Handle verse breakdown results."""
        breakdown = results.get('breakdown')
        if not breakdown:
            return

        self.current_verse_breakdown = breakdown
        verse_number = results.get('verse_number', 1)

        self.progress_label.setText(f"Verse {verse_number} breakdown complete")

        # Set up table for verse breakdown
        headers = ["Position", "Word/Number", "Type", "Language"]

        # Add gematria value columns
        if breakdown.calculation_types_used:
            for calc_type in breakdown.calculation_types_used:
                headers.append(f"Gematria ({calc_type})")

        self.word_table_model.setHorizontalHeaderLabels(headers)

        # Populate verse breakdown data
        for word_entry in breakdown.words:
            row_items = []

            # Position (1-based for display)
            position_item = QStandardItem(str(word_entry.position_in_verse + 1))
            position_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            row_items.append(position_item)

            # Word/Number
            word_item = QStandardItem(word_entry.word)
            if word_entry.is_number:
                word_item.setFont(QFont("Courier New", 10, QFont.Weight.Bold))
            row_items.append(word_item)

            # Type
            type_text = "Number" if word_entry.is_number else "Word"
            type_item = QStandardItem(type_text)
            type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            row_items.append(type_item)

            # Language
            lang_item = QStandardItem(word_entry.language.value if word_entry.language else "")
            row_items.append(lang_item)

            # Gematria values
            if breakdown.calculation_types_used:
                for calc_type in breakdown.calculation_types_used:
                    value = word_entry.gematria_values.get(calc_type, "")
                    row_items.append(QStandardItem(str(value) if value else ""))

            self.word_table_model.appendRow(row_items)

        # Resize columns
        self.results_table.resizeColumnsToContents()

        # Update details with verse summary
        self._display_verse_summary(breakdown)

    def _handle_concordance_results(self, results: Dict[str, Any]):
        """Handle concordance creation results."""
        concordance_table = results.get('concordance_table')
        if not concordance_table:
            return

        self.progress_label.setText("Concordance creation complete")

        # Set up table for concordance
        headers = ["Keyword", "Left Context", "Right Context", "Document", "Position"]
        self.word_table_model.setHorizontalHeaderLabels(headers)

        # Populate concordance data
        # Note: This would need to be adapted based on the actual concordance table structure
        # For now, showing a placeholder implementation

        placeholder_item = QStandardItem("Concordance results would be displayed here")
        self.word_table_model.appendRow([placeholder_item])

        # Update details
        details_text = f"Concordance '{concordance_table.name}' created successfully.\n"
        details_text += f"Keywords: {', '.join(concordance_table.keywords) if hasattr(concordance_table, 'keywords') else 'N/A'}\n"
        details_text += f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        self.details_text.setPlainText(details_text)

    def _display_global_analysis(self, global_analysis: GlobalAnalysisSummary):
        """Display global analysis summary in details panel."""
        summary_lines = [
            "GLOBAL ANALYSIS SUMMARY",
            "=" * 40,
            f"Total Verses: {global_analysis.total_verses:,}",
            f"Triangular Number: {global_analysis.triangular_number:,}",
            ""
        ]

        # Add calculation type totals
        for calc_type, total_sum in global_analysis.total_gematria_sums.items():
            global_sum = global_analysis.global_sums.get(calc_type, 0)
            summary_lines.extend([
                f"🔤 {calc_type}:",
                f"   • Total Gematria: {total_sum:,}",
                f"   • + Triangular:   {global_analysis.triangular_number:,}",
                f"   • = GLOBAL SUM:   {global_sum:,}",
                ""
            ])

        summary_text = "\n".join(summary_lines)
        self.details_text.setPlainText(summary_text)

    def _display_verse_summary(self, breakdown: VerseBreakdown):
        """Display verse breakdown summary in details panel."""
        summary_lines = [
            f"VERSE {breakdown.verse_number} BREAKDOWN",
            "=" * 40,
            f"Verse Text: {breakdown.verse_text}",
            "",
            f"Total Words: {len(breakdown.words)}",
            f"Word Count: {len([w for w in breakdown.words if not w.is_number])}",
            f"Number Count: {len([w for w in breakdown.words if w.is_number])}",
            ""
        ]

        # Calculate totals by type
        if breakdown.calculation_types_used:
            calc_type = breakdown.calculation_types_used[0]
            word_total = sum(
                w.gematria_values.get(calc_type, 0)
                for w in breakdown.words if not w.is_number
            )
            number_total = sum(
                w.gematria_values.get(calc_type, 0)
                for w in breakdown.words if w.is_number
            )

            summary_lines.extend([
                f"📝 Breakdown for {calc_type}:",
                f"   • Words: {word_total:,}",
                f"   • Numbers: {number_total:,}",
                f"   • Total: {word_total + number_total:,}"
            ])

        summary_text = "\n".join(summary_lines)
        self.details_text.setPlainText(summary_text)

    def _on_result_selection_changed(self):
        """Handle selection changes in the results table."""
        selection = self.results_table.selectionModel().selectedRows()
        if not selection:
            return

        # Get the selected row
        selected_index = selection[0]
        source_index = self.word_proxy_model.mapToSource(selected_index)

        # Get word from the first column
        word_item = self.word_table_model.item(source_index.row(), 0)
        if not word_item:
            return

        word = word_item.text()

        # Show word details based on current analysis type
        if self.current_gematria_result:
            self._show_word_details(word)
        elif self.current_verse_breakdown:
            self._show_verse_word_details(word, source_index.row())

    def _show_word_details(self, word: str):
        """Show details for a selected word from gematria analysis."""
        # Find the word entry
        word_entry = None
        for entry in self.current_gematria_result.entries:
            if entry.word == word:
                word_entry = entry
                break

        if not word_entry:
            return

        # Build details text
        details_lines = [
            f"WORD DETAILS: {word}",
            "=" * 40,
            f"Language: {word_entry.language.value}",
            f"Frequency: {word_entry.frequency}",
            f"Normalized: {word_entry.normalized_word}",
            ""
        ]

        # Add verse information
        if word_entry.verse_numbers:
            verses_text = ", ".join(map(str, sorted(word_entry.verse_numbers)))
            details_lines.extend([
                f"Appears in verses: {verses_text}",
                ""
            ])

        # Add gematria values
        details_lines.append("Gematria Values:")
        for calc_type, value in word_entry.gematria_values.items():
            details_lines.append(f"  {calc_type}: {value}")

        # Add occurrences if available
        if word_entry.occurrences:
            details_lines.extend([
                "",
                "Occurrences:",
            ])
            for i, occurrence in enumerate(word_entry.occurrences[:5]):  # Show first 5
                details_lines.append(f"  {i+1}. Verse {occurrence.verse_number}, Position {occurrence.position_in_verse}")

            if len(word_entry.occurrences) > 5:
                details_lines.append(f"  ... and {len(word_entry.occurrences) - 5} more")

        details_text = "\n".join(details_lines)
        self.details_text.setPlainText(details_text)

        # Emit word selected signal
        gematria_values = {calc_type: value for calc_type, value in word_entry.gematria_values.items()}
        self.word_selected.emit(word, gematria_values)

    def _show_verse_word_details(self, word: str, row_index: int):
        """Show details for a selected word from verse breakdown."""
        if row_index >= len(self.current_verse_breakdown.words):
            return

        word_entry = self.current_verse_breakdown.words[row_index]

        # Build details text
        details_lines = [
            f"WORD DETAILS: {word}",
            "=" * 40,
            f"Position in verse: {word_entry.position_in_verse + 1}",
            f"Type: {'Number' if word_entry.is_number else 'Word'}",
            f"Language: {word_entry.language.value if word_entry.language else 'N/A'}",
            ""
        ]

        # Add gematria values
        if word_entry.gematria_values:
            details_lines.append("Gematria Values:")
            for calc_type, value in word_entry.gematria_values.items():
                details_lines.append(f"  {calc_type}: {value}")

        details_text = "\n".join(details_lines)
        self.details_text.setPlainText(details_text)

    def _export_results(self):
        """Export analysis results."""
        if not (self.current_gematria_result or self.current_verse_breakdown):
            MessageBox.warning(self, "No Results", "No analysis results to export.")
            return

        # Determine export options based on current results
        export_options = []

        if self.current_gematria_result:
            export_options.extend([
                "CSV - Word Dictionary",
                "CSV - Verse Analysis",
                "JSON - Complete Analysis",
                "Text - Detailed Report"
            ])

        if self.current_verse_breakdown:
            export_options.extend([
                "CSV - Verse Breakdown",
                "Text - Verse Summary"
            ])

        if not export_options:
            MessageBox.warning(self, "No Export Options", "No export options available.")
            return

        # Show export format selection
        from PyQt6.QtWidgets import QInputDialog

        selected_format, ok = QInputDialog.getItem(
            self, "Export Format", "Choose export format:",
            export_options, 0, False
        )

        if not ok:
            return

        # Get export file path
        file_dialog = QFileDialog()
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)

        if selected_format.startswith("CSV"):
            file_filter = "CSV Files (*.csv)"
            default_ext = ".csv"
        elif selected_format.startswith("JSON"):
            file_filter = "JSON Files (*.json)"
            default_ext = ".json"
        else:
            file_filter = "Text Files (*.txt)"
            default_ext = ".txt"

        # Set default filename
        if self.current_gematria_result:
            default_name = f"{self.current_gematria_result.document_title}_analysis{default_ext}"
        else:
            default_name = f"verse_{self.current_verse_breakdown.verse_number}_breakdown{default_ext}"

        file_path, _ = file_dialog.getSaveFileName(
            self, "Export Results", default_name, file_filter
        )

        if not file_path:
            return

        try:
            # Perform export based on selected format
            if selected_format == "CSV - Word Dictionary":
                self._export_word_dictionary_csv(file_path)
            elif selected_format == "CSV - Verse Analysis":
                self._export_verse_analysis_csv(file_path)
            elif selected_format == "JSON - Complete Analysis":
                self._export_complete_json(file_path)
            elif selected_format == "Text - Detailed Report":
                self._export_detailed_report(file_path)
            elif selected_format == "CSV - Verse Breakdown":
                self._export_verse_breakdown_csv(file_path)
            elif selected_format == "Text - Verse Summary":
                self._export_verse_summary_text(file_path)

            MessageBox.information(
                self, "Export Successful",
                f"Results exported successfully to:\n{file_path}"
            )

        except Exception as e:
            logger.error(f"Error exporting results: {e}")
            MessageBox.error(self, "Export Error", f"Failed to export results: {str(e)}")

    def _export_word_dictionary_csv(self, file_path: str):
        """Export word dictionary to CSV."""
        if not self.current_gematria_result:
            return

        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['Word', 'Language', 'Frequency', 'Verses']

            # Add gematria columns
            for calc_type in sorted(self.current_gematria_result.calculation_types_used):
                columns.append(f'Gematria_{calc_type.replace(" ", "_")}')

            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(columns)

            # Write data
            for entry in self.current_gematria_result.entries:
                row = [
                    entry.word,
                    entry.language.value,
                    entry.frequency,
                    ', '.join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else ''
                ]

                # Add gematria values
                for calc_type in sorted(self.current_gematria_result.calculation_types_used):
                    value = entry.gematria_values.get(calc_type, '')
                    row.append(value)

                writer.writerow(row)

    def _export_verse_analysis_csv(self, file_path: str):
        """Export verse analysis to CSV."""
        if not self.current_gematria_result or not self.current_gematria_result.verse_analysis:
            return

        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['Verse_Number', 'Word_Count', 'Verse_Text']

            # Add gematria sum columns
            calc_types = list(self.current_gematria_result.verse_analysis[0].gematria_sums.keys())
            for calc_type in calc_types:
                columns.append(f'Gematria_{calc_type.replace(" ", "_")}')

            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(columns)

            # Write data
            for verse_entry in self.current_gematria_result.verse_analysis:
                row = [
                    verse_entry.verse_number,
                    verse_entry.word_count,
                    verse_entry.verse_text
                ]

                # Add gematria sums
                for calc_type in calc_types:
                    value = verse_entry.gematria_sums.get(calc_type, 0)
                    row.append(value)

                writer.writerow(row)

    def _export_complete_json(self, file_path: str):
        """Export complete analysis to JSON."""
        if not self.current_gematria_result:
            return

        # Convert result to dictionary
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'format_version': '1.0',
                'exported_by': 'IsopGem Unified Analysis Dialog'
            },
            'document_info': {
                'title': self.current_gematria_result.document_title,
                'total_words': self.current_gematria_result.total_words,
                'unique_words': self.current_gematria_result.unique_words,
                'calculation_types_used': self.current_gematria_result.calculation_types_used
            },
            'word_dictionary': [],
            'verse_analysis': [],
            'global_analysis': None
        }

        # Add word dictionary
        for entry in self.current_gematria_result.entries:
            word_data = {
                'word': entry.word,
                'normalized_word': entry.normalized_word,
                'language': entry.language.value,
                'frequency': entry.frequency,
                'gematria_values': entry.gematria_values,
                'verse_numbers': entry.verse_numbers,
                'occurrences': [
                    {
                        'verse_number': occ.verse_number,
                        'position_in_verse': occ.position_in_verse
                    } for occ in entry.occurrences
                ] if entry.occurrences else []
            }
            export_data['word_dictionary'].append(word_data)

        # Add verse analysis if available
        if self.current_gematria_result.verse_analysis:
            for verse_entry in self.current_gematria_result.verse_analysis:
                verse_data = {
                    'verse_number': verse_entry.verse_number,
                    'verse_text': verse_entry.verse_text,
                    'word_count': verse_entry.word_count,
                    'gematria_sums': verse_entry.gematria_sums
                }
                export_data['verse_analysis'].append(verse_data)

        # Add global analysis if available
        if self.current_gematria_result.global_analysis:
            global_analysis = self.current_gematria_result.global_analysis
            export_data['global_analysis'] = {
                'total_verses': global_analysis.total_verses,
                'triangular_number': global_analysis.triangular_number,
                'total_gematria_sums': global_analysis.total_gematria_sums,
                'global_sums': global_analysis.global_sums
            }

        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, indent=2, ensure_ascii=False)

    def _export_detailed_report(self, file_path: str):
        """Export detailed text report."""
        if not self.current_gematria_result:
            return

        with open(file_path, 'w', encoding='utf-8') as txtfile:
            # Header
            txtfile.write(f"GEMATRIA ANALYSIS REPORT\n")
            txtfile.write(f"Document: {self.current_gematria_result.document_title}\n")
            txtfile.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write("=" * 60 + "\n\n")

            # Summary
            txtfile.write("SUMMARY\n")
            txtfile.write("-" * 20 + "\n")
            txtfile.write(f"Total Words: {self.current_gematria_result.total_words:,}\n")
            txtfile.write(f"Unique Words: {self.current_gematria_result.unique_words:,}\n")
            txtfile.write(f"Calculation Types: {', '.join(self.current_gematria_result.calculation_types_used)}\n\n")

            # Word dictionary
            txtfile.write("WORD DICTIONARY\n")
            txtfile.write("-" * 20 + "\n")
            for entry in self.current_gematria_result.entries:
                txtfile.write(f"Word: {entry.word}\n")
                txtfile.write(f"  Language: {entry.language.value}\n")
                txtfile.write(f"  Frequency: {entry.frequency}\n")

                if entry.gematria_values:
                    txtfile.write("  Gematria Values:\n")
                    for calc_type, value in entry.gematria_values.items():
                        txtfile.write(f"    {calc_type}: {value}\n")

                if entry.verse_numbers:
                    verses_text = ', '.join(map(str, sorted(entry.verse_numbers)))
                    txtfile.write(f"  Verses: {verses_text}\n")

                txtfile.write("\n")

    def _export_verse_breakdown_csv(self, file_path: str):
        """Export verse breakdown to CSV."""
        if not self.current_verse_breakdown:
            return

        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['Position', 'Word', 'Type', 'Language']

            # Add gematria columns
            if self.current_verse_breakdown.calculation_types_used:
                for calc_type in self.current_verse_breakdown.calculation_types_used:
                    columns.append(f'Gematria_{calc_type.replace(" ", "_")}')

            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(columns)

            # Write data
            for word_entry in self.current_verse_breakdown.words:
                row = [
                    word_entry.position_in_verse + 1,  # 1-based position
                    word_entry.word,
                    'Number' if word_entry.is_number else 'Word',
                    word_entry.language.value if word_entry.language else ''
                ]

                # Add gematria values
                if self.current_verse_breakdown.calculation_types_used:
                    for calc_type in self.current_verse_breakdown.calculation_types_used:
                        value = word_entry.gematria_values.get(calc_type, '')
                        row.append(value)

                writer.writerow(row)

    def _export_verse_summary_text(self, file_path: str):
        """Export verse summary to text file."""
        if not self.current_verse_breakdown:
            return

        with open(file_path, 'w', encoding='utf-8') as txtfile:
            # Use the same summary as displayed in details
            summary_text = self.details_text.toPlainText()
            txtfile.write(summary_text)

    def _on_analysis_error(self, error_message: str):
        """Handle analysis errors."""
        self._enable_controls()
        self.progress_bar.setVisible(False)
        self.progress_label.setText("Analysis failed")

        MessageBox.error(
            self, "Analysis Error",
            f"An error occurred during analysis:\n\n{error_message}"
        )

    # Public API methods for external integration

    def set_text(self, text: str, title: str = ""):
        """Set text for analysis.

        Args:
            text: Text to analyze
            title: Document title
        """
        self.text_input.setPlainText(text)
        if title:
            self.title_input.setText(title)

    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """Get current analysis results.

        Returns:
            Current analysis results or None
        """
        if self.current_gematria_result:
            return {
                'type': 'gematria',
                'result': self.current_gematria_result
            }
        elif self.current_verse_breakdown:
            return {
                'type': 'verse_breakdown',
                'breakdown': self.current_verse_breakdown
            }
        return None
