"""
Unified Import Dialog - Consolidates all document import and conversion functionality.

This dialog combines the functionality of:
- ConvertAndSaveDialog: Document conversion and database saving
- DocumentConversionDialog: Document conversion for RTF editor
- EncodingConversionDialog: Text file encoding conversion

Key features:
- Tabbed interface for different import types
- Unified file selection and processing
- Progress tracking and error handling
- Flexible output options (database, RTF editor, encoding conversion)

Author: Assistant
Created: 2024-12-28
Dependencies: PyQt6, pathlib, typing, loguru
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple, Union

from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QStandardItemModel, QStandardItem
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QGroupBox, QFormLayout, QLineEdit, QTextEdit, QComboBox,
    QCheckBox, QSpinBox, QPushButton, QProgressBar, QLabel,
    QListWidget, QListWidgetItem, QTableView, QFileDialog,
    QMessageBox, QDialogButtonBox, QSplitter
)

from loguru import logger

from document_manager.models.document import DocumentType
from document_manager.services.document_service import DocumentService
from document_manager.services.category_service import CategoryService
from document_manager.services.document_conversion_service import DocumentConversionService
from shared.ui.components.message_box import MessageBox


class ImportWorker(QThread):
    """Worker thread for document import operations."""
    
    progress_updated = pyqtSignal(int, int, str)  # current, total, status
    import_completed = pyqtSignal(dict)  # Results dictionary
    error_occurred = pyqtSignal(str)  # Error message
    
    def __init__(self, import_config: Dict[str, Any]):
        """Initialize the import worker.
        
        Args:
            import_config: Configuration dictionary with import parameters
        """
        super().__init__()
        self.import_config = import_config
        self._stop_requested = False
        
        # Initialize services
        self.document_service = DocumentService()
        self.conversion_service = DocumentConversionService()
    
    def run(self):
        """Run the import process based on configuration."""
        try:
            import_type = self.import_config.get('type', 'standard')
            
            if import_type == 'standard':
                self._run_standard_import()
            elif import_type == 'conversion':
                self._run_conversion_import()
            elif import_type == 'encoding':
                self._run_encoding_conversion()
            else:
                self.error_occurred.emit(f"Unknown import type: {import_type}")
                
        except Exception as e:
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def _run_standard_import(self):
        """Run standard document import."""
        file_paths = self.import_config.get('file_paths', [])
        preserve_images = self.import_config.get('preserve_images', True)
        category_id = self.import_config.get('category_id')
        tags = self.import_config.get('tags', [])
        
        results = {
            'type': 'standard',
            'successful': [],
            'failed': [],
            'total': len(file_paths)
        }
        
        for i, file_path in enumerate(file_paths):
            if self._stop_requested:
                break
                
            self.progress_updated.emit(i + 1, len(file_paths), f"Importing {Path(file_path).name}")
            
            try:
                # Use enhanced import with image support
                if preserve_images and Path(file_path).suffix.lower() in ['.pdf', '.docx']:
                    document = self.document_service.import_document_with_images(
                        file_path, preserve_images=True
                    )
                else:
                    document = self.document_service.import_document(file_path)
                
                if document:
                    # Set category and tags if specified
                    if category_id:
                        document.category = category_id
                    if tags:
                        document.tags.update(tags)
                        
                    # Update document
                    self.document_service.update_document(document)
                    results['successful'].append(file_path)
                else:
                    results['failed'].append(file_path)
                    
            except Exception as e:
                logger.error(f"Error importing {file_path}: {e}")
                results['failed'].append(file_path)
        
        if not self._stop_requested:
            self.import_completed.emit(results)
    
    def _run_conversion_import(self):
        """Run document conversion for RTF editor."""
        file_paths = self.import_config.get('file_paths', [])
        preserve_formatting = self.import_config.get('preserve_formatting', True)
        save_to_database = self.import_config.get('save_to_database', False)
        
        results = {
            'type': 'conversion',
            'successful': [],
            'failed': [],
            'converted_documents': [],
            'total': len(file_paths)
        }
        
        for i, file_path in enumerate(file_paths):
            if self._stop_requested:
                break
                
            self.progress_updated.emit(i + 1, len(file_paths), f"Converting {Path(file_path).name}")
            
            try:
                doc_format = self.conversion_service.convert_file_to_document_format(
                    file_path, preserve_formatting=preserve_formatting
                )
                
                if doc_format:
                    results['successful'].append(file_path)
                    results['converted_documents'].append((file_path, doc_format))
                    
                    # Optionally save to database
                    if save_to_database:
                        category_id = self.import_config.get('category_id')
                        tags = self.import_config.get('tags', [])
                        notes = self.import_config.get('notes')
                        
                        saved_doc = self.document_service.save_converted_document(
                            doc_format,
                            original_file_path=file_path,
                            category=category_id,
                            tags=tags,
                            notes=notes
                        )
                        
                        if not saved_doc:
                            logger.warning(f"Failed to save converted document: {file_path}")
                else:
                    results['failed'].append(file_path)
                    
            except Exception as e:
                logger.error(f"Error converting {file_path}: {e}")
                results['failed'].append(file_path)
        
        if not self._stop_requested:
            self.import_completed.emit(results)
    
    def _run_encoding_conversion(self):
        """Run text file encoding conversion."""
        directory_path = self.import_config.get('directory_path')
        file_patterns = self.import_config.get('file_patterns', ['*.txt'])
        recursive = self.import_config.get('recursive', False)
        
        self.progress_updated.emit(0, 0, "Starting encoding conversion...")
        
        try:
            results = self.document_service.bulk_convert_text_files_to_utf8(
                directory_path, file_patterns, recursive
            )
            
            # Format results for consistency
            formatted_results = {
                'type': 'encoding',
                'total_files': results.get('total_files', 0),
                'converted_files': results.get('converted_files', 0),
                'already_utf8': results.get('already_utf8', 0),
                'failed_conversions': results.get('failed_conversions', 0),
                'conversion_details': results.get('conversion_details', []),
                'errors': results.get('errors', [])
            }
            
            if not self._stop_requested:
                self.import_completed.emit(formatted_results)
                
        except Exception as e:
            logger.error(f"Error during encoding conversion: {e}")
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def stop(self):
        """Request stopping the import process."""
        self._stop_requested = True


class UnifiedImportDialog(QDialog):
    """Unified dialog for all document import and conversion operations."""
    
    # Signals
    documents_imported = pyqtSignal(list)  # List of imported documents
    documents_converted = pyqtSignal(list)  # List of converted DocumentFormat objects
    encoding_conversion_completed = pyqtSignal(dict)  # Encoding conversion results
    
    def __init__(self, parent=None):
        """Initialize the unified import dialog."""
        super().__init__(parent)
        
        self.setWindowTitle("Import Documents")
        self.setModal(True)
        self.resize(1000, 700)
        
        # Services
        self.document_service = DocumentService()
        self.category_service = CategoryService()
        self.conversion_service = DocumentConversionService()
        
        # State
        self.import_worker: Optional[ImportWorker] = None
        self.selected_files: List[str] = []
        
        # Initialize UI
        self._init_ui()
        self._load_categories()
        self._connect_signals()
        
        logger.debug("UnifiedImportDialog initialized")
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_standard_import_tab()
        self._create_conversion_tab()
        self._create_encoding_tab()
        self._create_progress_tab()
        
        # Button box
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Close
        )
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def _create_standard_import_tab(self):
        """Create the standard document import tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # File selection section
        file_group = QGroupBox("File Selection")
        file_layout = QVBoxLayout(file_group)
        
        # File selection buttons
        button_layout = QHBoxLayout()
        
        self.add_files_btn = QPushButton("Add Files")
        self.add_files_btn.clicked.connect(self._add_files)
        button_layout.addWidget(self.add_files_btn)
        
        self.add_folder_btn = QPushButton("Add Folder")
        self.add_folder_btn.clicked.connect(self._add_folder)
        button_layout.addWidget(self.add_folder_btn)
        
        self.clear_files_btn = QPushButton("Clear")
        self.clear_files_btn.clicked.connect(self._clear_files)
        button_layout.addWidget(self.clear_files_btn)
        
        button_layout.addStretch()
        file_layout.addLayout(button_layout)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        file_layout.addWidget(self.file_list)
        
        # File count label
        self.file_count_label = QLabel("0 files selected")
        file_layout.addWidget(self.file_count_label)
        
        layout.addWidget(file_group)
        
        # Import options section
        options_group = QGroupBox("Import Options")
        options_layout = QFormLayout(options_group)
        
        # Category selection
        self.category_combo = QComboBox()
        options_layout.addRow("Category:", self.category_combo)
        
        # Tags
        self.tags_input = QLineEdit()
        self.tags_input.setPlaceholderText("Enter tags separated by commas")
        options_layout.addRow("Tags:", self.tags_input)
        
        # Image preservation
        self.preserve_images_check = QCheckBox("Preserve images (PDF/DOCX)")
        self.preserve_images_check.setChecked(True)
        self.preserve_images_check.setToolTip("Extract and embed images from PDF and DOCX files")
        options_layout.addRow("", self.preserve_images_check)
        
        layout.addWidget(options_group)
        
        # Import button
        self.import_btn = QPushButton("Import Documents")
        self.import_btn.setEnabled(False)
        self.import_btn.clicked.connect(self._start_standard_import)
        layout.addWidget(self.import_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Standard Import")
    
    def _create_conversion_tab(self):
        """Create the document conversion tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Description
        desc_label = QLabel(
            "Convert documents to RTF editor format. Optionally save to database."
        )
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # File selection (reuse from standard import)
        file_group = QGroupBox("File Selection")
        file_layout = QVBoxLayout(file_group)
        
        # Conversion file list
        self.conversion_file_list = QListWidget()
        self.conversion_file_list.setMaximumHeight(150)
        file_layout.addWidget(self.conversion_file_list)
        
        # File selection buttons for conversion
        conv_button_layout = QHBoxLayout()
        
        self.conv_add_files_btn = QPushButton("Add Files")
        self.conv_add_files_btn.clicked.connect(self._add_conversion_files)
        conv_button_layout.addWidget(self.conv_add_files_btn)
        
        self.conv_clear_btn = QPushButton("Clear")
        self.conv_clear_btn.clicked.connect(self._clear_conversion_files)
        conv_button_layout.addWidget(self.conv_clear_btn)
        
        conv_button_layout.addStretch()
        file_layout.addLayout(conv_button_layout)
        
        self.conv_file_count_label = QLabel("0 files selected")
        file_layout.addWidget(self.conv_file_count_label)
        
        layout.addWidget(file_group)
        
        # Conversion options
        conv_options_group = QGroupBox("Conversion Options")
        conv_options_layout = QFormLayout(conv_options_group)
        
        # Preserve formatting
        self.preserve_formatting_check = QCheckBox("Preserve formatting")
        self.preserve_formatting_check.setChecked(True)
        conv_options_layout.addRow("", self.preserve_formatting_check)
        
        # Save to database option
        self.save_to_db_check = QCheckBox("Save to database")
        self.save_to_db_check.setToolTip("Also save converted documents to the database")
        conv_options_layout.addRow("", self.save_to_db_check)
        
        # Database options (enabled when save_to_db is checked)
        self.conv_category_combo = QComboBox()
        self.conv_category_combo.setEnabled(False)
        conv_options_layout.addRow("Category:", self.conv_category_combo)
        
        self.conv_tags_input = QLineEdit()
        self.conv_tags_input.setPlaceholderText("Enter tags separated by commas")
        self.conv_tags_input.setEnabled(False)
        conv_options_layout.addRow("Tags:", self.conv_tags_input)
        
        self.conv_notes_input = QTextEdit()
        self.conv_notes_input.setMaximumHeight(60)
        self.conv_notes_input.setPlaceholderText("Optional notes")
        self.conv_notes_input.setEnabled(False)
        conv_options_layout.addRow("Notes:", self.conv_notes_input)
        
        # Connect save to database checkbox
        self.save_to_db_check.toggled.connect(self._toggle_db_options)
        
        layout.addWidget(conv_options_group)
        
        # Convert button
        self.convert_btn = QPushButton("Convert Documents")
        self.convert_btn.setEnabled(False)
        self.convert_btn.clicked.connect(self._start_conversion)
        layout.addWidget(self.convert_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Document Conversion")

    def _create_encoding_tab(self):
        """Create the encoding conversion tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Description
        desc_label = QLabel(
            "Convert text files with various encodings (Windows-1252, ISO-8859-1, etc.) to UTF-8. "
            "Original files will be backed up."
        )
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # Directory selection
        dir_group = QGroupBox("Directory Selection")
        dir_layout = QVBoxLayout(dir_group)

        dir_select_layout = QHBoxLayout()
        self.directory_input = QLineEdit()
        self.directory_input.setPlaceholderText("Select directory containing text files")
        self.directory_input.setReadOnly(True)
        dir_select_layout.addWidget(self.directory_input)

        self.browse_dir_btn = QPushButton("Browse")
        self.browse_dir_btn.clicked.connect(self._browse_directory)
        dir_select_layout.addWidget(self.browse_dir_btn)

        dir_layout.addLayout(dir_select_layout)
        layout.addWidget(dir_group)

        # Encoding options
        enc_options_group = QGroupBox("Conversion Options")
        enc_options_layout = QFormLayout(enc_options_group)

        # File patterns
        self.file_patterns_input = QLineEdit("*.txt")
        self.file_patterns_input.setPlaceholderText("*.txt, *.md, *.log")
        enc_options_layout.addRow("File patterns:", self.file_patterns_input)

        # Recursive option
        self.recursive_check = QCheckBox("Include subdirectories")
        enc_options_layout.addRow("", self.recursive_check)

        layout.addWidget(enc_options_group)

        # Convert button
        self.encoding_convert_btn = QPushButton("Convert Encoding")
        self.encoding_convert_btn.setEnabled(False)
        self.encoding_convert_btn.clicked.connect(self._start_encoding_conversion)
        layout.addWidget(self.encoding_convert_btn)

        layout.addStretch()
        self.tab_widget.addTab(tab, "Encoding Conversion")

    def _create_progress_tab(self):
        """Create the progress tracking tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Progress section
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_label = QLabel("Ready to import")
        progress_layout.addWidget(self.progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # Cancel button
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self._cancel_operation)
        progress_layout.addWidget(self.cancel_btn)

        layout.addWidget(progress_group)

        # Results section
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)

        self.results_list = QListWidget()
        results_layout.addWidget(self.results_list)

        layout.addWidget(results_group)

        self.tab_widget.addTab(tab, "Progress")

    def _load_categories(self):
        """Load categories for the combo boxes."""
        categories = self.category_service.get_all_categories()

        # Standard import categories
        self.category_combo.clear()
        self.category_combo.addItem("None", None)
        for category in categories:
            self.category_combo.addItem(category.name, category.id)

        # Conversion categories
        self.conv_category_combo.clear()
        self.conv_category_combo.addItem("None", None)
        for category in categories:
            self.conv_category_combo.addItem(category.name, category.id)

    def _connect_signals(self):
        """Connect UI signals to their handlers."""
        # File selection changes
        self.file_list.itemSelectionChanged.connect(self._update_import_button)
        self.conversion_file_list.itemSelectionChanged.connect(self._update_convert_button)
        self.directory_input.textChanged.connect(self._update_encoding_button)

    def _add_files(self):
        """Add files for standard import."""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)

        # Get supported file types
        supported_extensions = [f"*.{t.value}" for t in DocumentType]
        file_types = f"Documents ({' '.join(supported_extensions)})"

        file_paths, _ = file_dialog.getOpenFileNames(
            self, "Select Documents to Import", "", file_types
        )

        if file_paths:
            for file_path in file_paths:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
                    self.file_list.addItem(Path(file_path).name)

            self._update_file_count()

    def _add_folder(self):
        """Add all supported files from a folder."""
        folder_path = QFileDialog.getExistingDirectory(
            self, "Select Folder"
        )

        if folder_path:
            folder = Path(folder_path)
            supported_extensions = [f".{t.value}" for t in DocumentType]

            for file_path in folder.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
                        self.file_list.addItem(file_path.name)

            self._update_file_count()

    def _clear_files(self):
        """Clear selected files."""
        self.selected_files.clear()
        self.file_list.clear()
        self._update_file_count()

    def _update_file_count(self):
        """Update the file count label."""
        count = len(self.selected_files)
        self.file_count_label.setText(f"{count} file{'s' if count != 1 else ''} selected")
        self._update_import_button()

    def _add_conversion_files(self):
        """Add files for conversion."""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)

        # Get supported file types for conversion
        file_types = "Documents (*.pdf *.docx *.txt *.odt *.ods *.odp)"

        file_paths, _ = file_dialog.getOpenFileNames(
            self, "Select Documents to Convert", "", file_types
        )

        if file_paths:
            for file_path in file_paths:
                # Check if already in list
                existing_items = [self.conversion_file_list.item(i).data(Qt.ItemDataRole.UserRole)
                                for i in range(self.conversion_file_list.count())]
                if file_path not in existing_items:
                    item = QListWidgetItem(Path(file_path).name)
                    item.setData(Qt.ItemDataRole.UserRole, file_path)
                    self.conversion_file_list.addItem(item)

            self._update_conversion_file_count()

    def _clear_conversion_files(self):
        """Clear conversion files."""
        self.conversion_file_list.clear()
        self._update_conversion_file_count()

    def _update_conversion_file_count(self):
        """Update the conversion file count label."""
        count = self.conversion_file_list.count()
        self.conv_file_count_label.setText(f"{count} file{'s' if count != 1 else ''} selected")
        self._update_convert_button()

    def _browse_directory(self):
        """Browse for directory for encoding conversion."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory for Encoding Conversion"
        )

        if directory:
            self.directory_input.setText(directory)

    def _toggle_db_options(self, enabled: bool):
        """Toggle database options based on save to database checkbox."""
        self.conv_category_combo.setEnabled(enabled)
        self.conv_tags_input.setEnabled(enabled)
        self.conv_notes_input.setEnabled(enabled)

    def _update_import_button(self):
        """Update the import button state."""
        self.import_btn.setEnabled(len(self.selected_files) > 0)

    def _update_convert_button(self):
        """Update the convert button state."""
        self.convert_btn.setEnabled(self.conversion_file_list.count() > 0)

    def _update_encoding_button(self):
        """Update the encoding convert button state."""
        self.encoding_convert_btn.setEnabled(bool(self.directory_input.text().strip()))

    def _start_standard_import(self):
        """Start standard document import."""
        if not self.selected_files:
            return

        # Get import configuration
        category_idx = self.category_combo.currentIndex()
        category_id = self.category_combo.itemData(category_idx) if category_idx > 0 else None

        tags = [tag.strip() for tag in self.tags_input.text().split(',') if tag.strip()]
        preserve_images = self.preserve_images_check.isChecked()

        import_config = {
            'type': 'standard',
            'file_paths': self.selected_files.copy(),
            'category_id': category_id,
            'tags': tags,
            'preserve_images': preserve_images
        }

        self._start_import_worker(import_config)

    def _start_conversion(self):
        """Start document conversion."""
        if self.conversion_file_list.count() == 0:
            return

        # Get file paths from list
        file_paths = []
        for i in range(self.conversion_file_list.count()):
            item = self.conversion_file_list.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path:
                file_paths.append(file_path)

        # Get conversion configuration
        preserve_formatting = self.preserve_formatting_check.isChecked()
        save_to_database = self.save_to_db_check.isChecked()

        import_config = {
            'type': 'conversion',
            'file_paths': file_paths,
            'preserve_formatting': preserve_formatting,
            'save_to_database': save_to_database
        }

        # Add database options if saving to database
        if save_to_database:
            category_idx = self.conv_category_combo.currentIndex()
            category_id = self.conv_category_combo.itemData(category_idx) if category_idx > 0 else None

            tags = [tag.strip() for tag in self.conv_tags_input.text().split(',') if tag.strip()]
            notes = self.conv_notes_input.toPlainText().strip() or None

            import_config.update({
                'category_id': category_id,
                'tags': tags,
                'notes': notes
            })

        self._start_import_worker(import_config)

    def _start_encoding_conversion(self):
        """Start encoding conversion."""
        directory_path = self.directory_input.text().strip()
        if not directory_path:
            return

        # Get file patterns
        patterns_text = self.file_patterns_input.text().strip()
        file_patterns = [p.strip() for p in patterns_text.split(',') if p.strip()]
        if not file_patterns:
            file_patterns = ['*.txt']

        recursive = self.recursive_check.isChecked()

        import_config = {
            'type': 'encoding',
            'directory_path': directory_path,
            'file_patterns': file_patterns,
            'recursive': recursive
        }

        self._start_import_worker(import_config)

    def _start_import_worker(self, import_config: Dict[str, Any]):
        """Start the import worker with the given configuration."""
        # Switch to progress tab
        self.tab_widget.setCurrentIndex(3)

        # Update UI for operation
        self._disable_controls()
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate initially
        self.cancel_btn.setEnabled(True)
        self.results_list.clear()

        # Start worker
        self.import_worker = ImportWorker(import_config)
        self.import_worker.progress_updated.connect(self._on_progress_updated)
        self.import_worker.import_completed.connect(self._on_import_completed)
        self.import_worker.error_occurred.connect(self._on_import_error)
        self.import_worker.start()

        operation_type = import_config.get('type', 'operation')
        self.progress_label.setText(f"Starting {operation_type}...")

    def _disable_controls(self):
        """Disable all import controls during operation."""
        self.import_btn.setEnabled(False)
        self.convert_btn.setEnabled(False)
        self.encoding_convert_btn.setEnabled(False)
        self.add_files_btn.setEnabled(False)
        self.add_folder_btn.setEnabled(False)
        self.clear_files_btn.setEnabled(False)
        self.conv_add_files_btn.setEnabled(False)
        self.conv_clear_btn.setEnabled(False)
        self.browse_dir_btn.setEnabled(False)

    def _enable_controls(self):
        """Re-enable all import controls after operation."""
        self.add_files_btn.setEnabled(True)
        self.add_folder_btn.setEnabled(True)
        self.clear_files_btn.setEnabled(True)
        self.conv_add_files_btn.setEnabled(True)
        self.conv_clear_btn.setEnabled(True)
        self.browse_dir_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)

        # Update button states based on current selections
        self._update_import_button()
        self._update_convert_button()
        self._update_encoding_button()

    def _cancel_operation(self):
        """Cancel the current import operation."""
        if self.import_worker and self.import_worker.isRunning():
            self.import_worker.stop()
            self.import_worker.wait(3000)  # Wait up to 3 seconds

        self._enable_controls()
        self.progress_bar.setVisible(False)
        self.progress_label.setText("Operation cancelled")

        # Add cancellation message to results
        item = QListWidgetItem("❌ Operation cancelled by user")
        self.results_list.addItem(item)

    def _on_progress_updated(self, current: int, total: int, status: str):
        """Handle progress updates from the worker."""
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
            self.progress_label.setText(f"{status} ({current}/{total})")
        else:
            self.progress_label.setText(status)

    def _on_import_completed(self, results: Dict[str, Any]):
        """Handle import completion."""
        self._enable_controls()
        self.progress_bar.setVisible(False)

        import_type = results.get('type', 'operation')

        if import_type == 'standard':
            self._handle_standard_import_results(results)
        elif import_type == 'conversion':
            self._handle_conversion_results(results)
        elif import_type == 'encoding':
            self._handle_encoding_results(results)

    def _handle_standard_import_results(self, results: Dict[str, Any]):
        """Handle standard import results."""
        successful = results.get('successful', [])
        failed = results.get('failed', [])
        total = results.get('total', 0)

        self.progress_label.setText(
            f"Import complete: {len(successful)} successful, {len(failed)} failed"
        )

        # Add results to list
        for file_path in successful:
            item = QListWidgetItem(f"✅ {Path(file_path).name}")
            item.setToolTip(f"Successfully imported: {file_path}")
            self.results_list.addItem(item)

        for file_path in failed:
            item = QListWidgetItem(f"❌ {Path(file_path).name}")
            item.setToolTip(f"Failed to import: {file_path}")
            self.results_list.addItem(item)

        # Show completion message
        if failed:
            MessageBox.warning(
                self, "Import Completed with Errors",
                f"Imported {len(successful)} of {total} documents.\n"
                f"{len(failed)} documents failed to import."
            )
        else:
            MessageBox.information(
                self, "Import Successful",
                f"Successfully imported {len(successful)} documents."
            )

        # Emit signal
        self.documents_imported.emit(successful)

    def _handle_conversion_results(self, results: Dict[str, Any]):
        """Handle conversion results."""
        successful = results.get('successful', [])
        failed = results.get('failed', [])
        converted_documents = results.get('converted_documents', [])
        total = results.get('total', 0)

        self.progress_label.setText(
            f"Conversion complete: {len(successful)} successful, {len(failed)} failed"
        )

        # Add results to list
        for file_path in successful:
            item = QListWidgetItem(f"✅ {Path(file_path).name}")
            item.setToolTip(f"Successfully converted: {file_path}")
            self.results_list.addItem(item)

        for file_path in failed:
            item = QListWidgetItem(f"❌ {Path(file_path).name}")
            item.setToolTip(f"Failed to convert: {file_path}")
            self.results_list.addItem(item)

        # Show completion message
        if failed:
            MessageBox.warning(
                self, "Conversion Completed with Errors",
                f"Converted {len(successful)} of {total} documents.\n"
                f"{len(failed)} documents failed to convert."
            )
        else:
            MessageBox.information(
                self, "Conversion Successful",
                f"Successfully converted {len(successful)} documents."
            )

        # Emit signal with DocumentFormat objects
        doc_formats = [doc_format for _, doc_format in converted_documents if doc_format]
        self.documents_converted.emit(doc_formats)

    def _handle_encoding_results(self, results: Dict[str, Any]):
        """Handle encoding conversion results."""
        total_files = results.get('total_files', 0)
        converted_files = results.get('converted_files', 0)
        already_utf8 = results.get('already_utf8', 0)
        failed_conversions = results.get('failed_conversions', 0)
        conversion_details = results.get('conversion_details', [])

        self.progress_label.setText(
            f"Encoding conversion complete: {converted_files} converted, "
            f"{already_utf8} already UTF-8, {failed_conversions} failed"
        )

        # Add results to list
        for detail in conversion_details:
            file_name = Path(detail['file_path']).name
            status = detail['status']

            if status == 'converted':
                original_encoding = detail.get('original_encoding', 'unknown')
                item_text = f"✅ {file_name} (from {original_encoding})"
            elif status == 'already_utf8':
                item_text = f"ℹ️ {file_name} (already UTF-8)"
            elif status == 'failed':
                item_text = f"❌ {file_name} (failed)"
            else:
                item_text = f"? {file_name} ({status})"

            item = QListWidgetItem(item_text)
            item.setToolTip(f"File: {detail['file_path']}")
            self.results_list.addItem(item)

        # Show completion message
        if failed_conversions == 0:
            MessageBox.information(
                self, "Encoding Conversion Successful",
                f"Successfully processed {total_files} files!\n"
                f"Converted: {converted_files}\n"
                f"Already UTF-8: {already_utf8}"
            )
        else:
            MessageBox.warning(
                self, "Encoding Conversion Completed with Errors",
                f"Processed {total_files} files with {failed_conversions} failures.\n"
                f"Converted: {converted_files}\n"
                f"Already UTF-8: {already_utf8}\n"
                f"Failed: {failed_conversions}"
            )

        # Emit signal
        self.encoding_conversion_completed.emit(results)

    def _on_import_error(self, error_message: str):
        """Handle import errors."""
        self._enable_controls()
        self.progress_bar.setVisible(False)
        self.progress_label.setText("Operation failed")

        # Add error to results
        item = QListWidgetItem(f"❌ Error: {error_message}")
        self.results_list.addItem(item)

        MessageBox.error(
            self, "Import Error",
            f"An error occurred during the operation:\n\n{error_message}"
        )
