"""
Convert and Save Dialog

This dialog allows users to convert documents and save them to the database
with metadata and categorization options.
"""

from pathlib import Path
from typing import List, Optional

from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QComboBox, QTextEdit, QCheckBox, QProgressBar, QGroupBox, QFormLayout,
    QFileDialog, QListWidget, QListWidgetItem, QMessageBox, QTabWidget,
    QWidget, QSpinBox
)

from loguru import logger

from document_manager.services.document_conversion_service import DocumentConversionService
from document_manager.services.converted_document_service import ConvertedDocumentService
from document_manager.services.category_service import CategoryService
from shared.ui.widgets.rtf_editor.models.document_format import DocumentFormat


class ConversionWorker(QThread):
    """Worker thread for document conversion."""
    
    progress_updated = pyqtSignal(int, int, str)  # current, total, message
    conversion_completed = pyqtSignal(list)  # List of (file_path, DocumentFormat)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_paths: List[str], preserve_formatting: bool = True):
        super().__init__()
        self.file_paths = file_paths
        self.preserve_formatting = preserve_formatting
        self.conversion_service = DocumentConversionService()
    
    def run(self):
        """Run the conversion process."""
        try:
            results = []
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                self.progress_updated.emit(i + 1, total_files, f"Converting {Path(file_path).name}")
                
                try:
                    doc_format = self.conversion_service.convert_file_to_document_format(
                        file_path, 
                        preserve_formatting=self.preserve_formatting
                    )
                    results.append((file_path, doc_format))
                except Exception as e:
                    logger.error(f"Error converting {file_path}: {e}")
                    results.append((file_path, None))
            
            self.conversion_completed.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class ConvertAndSaveDialog(QDialog):
    """Dialog for converting documents and saving them to the database."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Convert and Save Documents")
        self.setModal(True)
        self.resize(800, 600)
        
        # Services
        self.conversion_service = DocumentConversionService()
        self.save_service = ConvertedDocumentService()
        self.category_service = CategoryService()
        
        # State
        self.file_paths: List[str] = []
        self.converted_documents: List[tuple] = []  # (file_path, DocumentFormat)
        self.conversion_worker: Optional[ConversionWorker] = None
        
        # UI
        self._init_ui()
        self._load_categories()
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title_label = QLabel("Convert and Save Documents to Database")
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # File Selection Tab
        self._create_file_selection_tab()
        
        # Conversion Options Tab
        self._create_conversion_options_tab()
        
        # Metadata Tab
        self._create_metadata_tab()
        
        # Progress Tab
        self._create_progress_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.convert_button = QPushButton("Start Conversion")
        self.convert_button.clicked.connect(self._start_conversion)
        self.convert_button.setEnabled(False)
        button_layout.addWidget(self.convert_button)
        
        self.save_button = QPushButton("Save to Database")
        self.save_button.clicked.connect(self._save_documents)
        self.save_button.setEnabled(False)
        button_layout.addWidget(self.save_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def _create_file_selection_tab(self):
        """Create the file selection tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # File selection controls
        file_controls = QHBoxLayout()
        
        self.add_files_button = QPushButton("Add Files...")
        self.add_files_button.clicked.connect(self._add_files)
        file_controls.addWidget(self.add_files_button)
        
        self.add_folder_button = QPushButton("Add Folder...")
        self.add_folder_button.clicked.connect(self._add_folder)
        file_controls.addWidget(self.add_folder_button)
        
        self.clear_files_button = QPushButton("Clear All")
        self.clear_files_button.clicked.connect(self._clear_files)
        file_controls.addWidget(self.clear_files_button)
        
        file_controls.addStretch()
        layout.addLayout(file_controls)
        
        # File list
        self.file_list = QListWidget()
        layout.addWidget(self.file_list)
        
        # File count label
        self.file_count_label = QLabel("No files selected")
        layout.addWidget(self.file_count_label)
        
        self.tab_widget.addTab(tab, "Files")
    
    def _create_conversion_options_tab(self):
        """Create the conversion options tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Conversion options group
        options_group = QGroupBox("Conversion Options")
        options_layout = QFormLayout()
        
        self.preserve_formatting = QCheckBox("Preserve formatting")
        self.preserve_formatting.setChecked(True)
        self.preserve_formatting.setToolTip("Preserve original document formatting and images")
        options_layout.addRow("", self.preserve_formatting)
        
        self.extract_images = QCheckBox("Extract images")
        self.extract_images.setChecked(True)
        self.extract_images.setToolTip("Extract and embed images from documents")
        options_layout.addRow("", self.extract_images)
        
        self.process_tables = QCheckBox("Process tables")
        self.process_tables.setChecked(True)
        self.process_tables.setToolTip("Preserve table structure in converted documents")
        options_layout.addRow("", self.process_tables)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Quality settings
        quality_group = QGroupBox("Quality Settings")
        quality_layout = QFormLayout()
        
        self.image_quality = QSpinBox()
        self.image_quality.setRange(50, 100)
        self.image_quality.setValue(85)
        self.image_quality.setSuffix("%")
        quality_layout.addRow("Image Quality:", self.image_quality)
        
        self.max_image_size = QSpinBox()
        self.max_image_size.setRange(100, 2000)
        self.max_image_size.setValue(800)
        self.max_image_size.setSuffix(" px")
        quality_layout.addRow("Max Image Width:", self.max_image_size)
        
        quality_group.setLayout(quality_layout)
        layout.addWidget(quality_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Options")
    
    def _create_metadata_tab(self):
        """Create the metadata tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Metadata group
        metadata_group = QGroupBox("Document Metadata")
        metadata_layout = QFormLayout()
        
        # Category
        self.category_combo = QComboBox()
        metadata_layout.addRow("Category:", self.category_combo)
        
        # Tags
        self.tags_input = QLineEdit()
        self.tags_input.setPlaceholderText("Enter tags separated by commas")
        metadata_layout.addRow("Tags:", self.tags_input)
        
        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setPlaceholderText("Additional notes about these documents...")
        metadata_layout.addRow("Notes:", self.notes_input)
        
        metadata_group.setLayout(metadata_layout)
        layout.addWidget(metadata_group)
        
        # Auto-categorization
        auto_group = QGroupBox("Auto-Categorization")
        auto_layout = QVBoxLayout()
        
        self.auto_categorize = QCheckBox("Automatically categorize based on content")
        auto_layout.addWidget(self.auto_categorize)
        
        self.auto_tag = QCheckBox("Automatically generate tags")
        auto_layout.addWidget(self.auto_tag)
        
        auto_group.setLayout(auto_layout)
        layout.addWidget(auto_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Metadata")
    
    def _create_progress_tab(self):
        """Create the progress tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Progress information
        self.progress_label = QLabel("Ready to convert documents")
        layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # Results area
        results_label = QLabel("Conversion Results:")
        results_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(results_label)
        
        self.results_list = QListWidget()
        layout.addWidget(self.results_list)
        
        # Statistics
        self.stats_label = QLabel("")
        layout.addWidget(self.stats_label)
        
        self.tab_widget.addTab(tab, "Progress")
    
    def _load_categories(self):
        """Load categories into the combo box."""
        self.category_combo.clear()
        self.category_combo.addItem("No Category", None)
        
        try:
            categories = self.category_service.get_all_categories()
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        except Exception as e:
            logger.error(f"Error loading categories: {e}")
    
    def _add_files(self):
        """Add files to the conversion list."""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Documents to Convert",
            "",
            "Documents (*.pdf *.docx *.txt *.html *.md);;All Files (*)"
        )
        
        for file_path in file_paths:
            if file_path not in self.file_paths:
                self.file_paths.append(file_path)
                item = QListWidgetItem(Path(file_path).name)
                item.setToolTip(file_path)
                self.file_list.addItem(item)
        
        self._update_file_count()
    
    def _add_folder(self):
        """Add all supported files from a folder."""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "Select Folder to Convert"
        )
        
        if folder_path:
            folder = Path(folder_path)
            supported_extensions = {'.pdf', '.docx', '.txt', '.html', '.md'}
            
            for file_path in folder.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    file_str = str(file_path)
                    if file_str not in self.file_paths:
                        self.file_paths.append(file_str)
                        item = QListWidgetItem(file_path.name)
                        item.setToolTip(file_str)
                        self.file_list.addItem(item)
            
            self._update_file_count()
    
    def _clear_files(self):
        """Clear all files from the list."""
        self.file_paths.clear()
        self.file_list.clear()
        self._update_file_count()
    
    def _update_file_count(self):
        """Update the file count label."""
        count = len(self.file_paths)
        self.file_count_label.setText(f"{count} file{'s' if count != 1 else ''} selected")
        self.convert_button.setEnabled(count > 0)
    
    def _start_conversion(self):
        """Start the document conversion process."""
        if not self.file_paths:
            return
        
        # Switch to progress tab
        self.tab_widget.setCurrentIndex(3)
        
        # Disable controls
        self.convert_button.setEnabled(False)
        self.add_files_button.setEnabled(False)
        self.add_folder_button.setEnabled(False)
        
        # Clear previous results
        self.results_list.clear()
        self.converted_documents.clear()
        
        # Start conversion worker
        self.conversion_worker = ConversionWorker(
            self.file_paths,
            self.preserve_formatting.isChecked()
        )
        self.conversion_worker.progress_updated.connect(self._on_progress_updated)
        self.conversion_worker.conversion_completed.connect(self._on_conversion_completed)
        self.conversion_worker.error_occurred.connect(self._on_conversion_error)
        self.conversion_worker.start()
    
    def _on_progress_updated(self, current: int, total: int, message: str):
        """Handle progress updates."""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.progress_label.setText(f"{message} ({current}/{total})")
    
    def _on_conversion_completed(self, results: List[tuple]):
        """Handle conversion completion."""
        self.converted_documents = results
        
        # Update results list
        successful = 0
        failed = 0
        
        for file_path, doc_format in results:
            file_name = Path(file_path).name
            if doc_format:
                item = QListWidgetItem(f"✓ {file_name}")
                item.setToolTip(f"Successfully converted: {file_path}")
                successful += 1
            else:
                item = QListWidgetItem(f"✗ {file_name}")
                item.setToolTip(f"Failed to convert: {file_path}")
                failed += 1
            
            self.results_list.addItem(item)
        
        # Update statistics
        self.stats_label.setText(f"Conversion completed: {successful} successful, {failed} failed")
        self.progress_label.setText("Conversion completed")
        
        # Enable save button if we have successful conversions
        self.save_button.setEnabled(successful > 0)
        
        # Re-enable controls
        self.convert_button.setEnabled(True)
        self.add_files_button.setEnabled(True)
        self.add_folder_button.setEnabled(True)
    
    def _on_conversion_error(self, error_message: str):
        """Handle conversion errors."""
        self.progress_label.setText(f"Conversion error: {error_message}")
        self.stats_label.setText("Conversion failed")
        
        # Re-enable controls
        self.convert_button.setEnabled(True)
        self.add_files_button.setEnabled(True)
        self.add_folder_button.setEnabled(True)
    
    def _save_documents(self):
        """Save converted documents to the database."""
        if not self.converted_documents:
            return
        
        try:
            # Get metadata
            category = self.category_combo.currentData()
            tags = [tag.strip() for tag in self.tags_input.text().split(',') if tag.strip()]
            notes = self.notes_input.toPlainText().strip() or None
            
            saved_count = 0
            failed_count = 0
            
            for file_path, doc_format in self.converted_documents:
                if doc_format:
                    saved_doc = self.save_service.save_converted_document(
                        doc_format,
                        original_file_path=file_path,
                        category=category,
                        tags=tags,
                        notes=notes
                    )
                    
                    if saved_doc:
                        saved_count += 1
                    else:
                        failed_count += 1
            
            # Show results
            if saved_count > 0:
                QMessageBox.information(
                    self,
                    "Save Complete",
                    f"Successfully saved {saved_count} document{'s' if saved_count != 1 else ''} to the database."
                    + (f"\n{failed_count} document{'s' if failed_count != 1 else ''} failed to save." if failed_count > 0 else "")
                )
            else:
                QMessageBox.warning(
                    self,
                    "Save Failed",
                    "No documents were saved to the database."
                )
            
            # Disable save button
            self.save_button.setEnabled(False)
            
        except Exception as e:
            logger.error(f"Error saving documents: {e}")
            QMessageBox.critical(
                self,
                "Save Error",
                f"An error occurred while saving documents:\n{str(e)}"
            )
