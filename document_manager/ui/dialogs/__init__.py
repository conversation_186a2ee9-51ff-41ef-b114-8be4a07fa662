"""
UI dialogs for the document manager pillar.

This module provides dialog classes for document management operations.

Note: Legacy dialogs have been consolidated into unified dialogs:
- ConvertAndSaveDialog, DocumentConversionDialog, EncodingConversionDialog → UnifiedImportDialog
- GematriaDictionaryDialog, VerseBreakdownDialog, ConcordanceCreationDialog → UnifiedAnalysisDialog
"""

from document_manager.ui.dialogs.unified_import_dialog import UnifiedImportDialog
from document_manager.ui.dialogs.unified_analysis_dialog import UnifiedAnalysisDialog

__all__ = [
    "UnifiedImportDialog",
    "UnifiedAnalysisDialog",
]
