"""
Advanced Document Search Panel

This panel provides comprehensive search functionality for documents in the database,
including phrase search, position tracking, and result navigation.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple

from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor, QTextCursor
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QCheckBox, QComboBox, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QSpinBox, QDateEdit, QTabWidget,
    QProgressBar, QFrame, QScrollArea
)

from loguru import logger

from document_manager.models.document import Document, DocumentType
from document_manager.services.converted_document_service import ConvertedDocumentService
from document_manager.services.category_service import CategoryService
from document_manager.ui.utils.document_preview_utils import DocumentPreviewRenderer
from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


class AdvancedDocumentSearchPanel(QWidget):
    """Advanced search panel for documents with full-text search and navigation."""
    
    # Signals
    document_selected = pyqtSignal(str)  # Document ID
    search_completed = pyqtSignal(int)   # Number of results
    
    def __init__(self, parent=None):
        """Initialize the advanced search panel."""
        super().__init__(parent)
        self.setWindowTitle("Advanced Document Search")
        
        # Services
        self.document_service = ConvertedDocumentService()
        self.category_service = CategoryService()
        
        # State
        self.current_results: List[Tuple[Document, List[Dict]]] = []
        self.current_document: Optional[Document] = None
        self.current_matches: List[Dict] = []
        self.current_match_index: int = -1
        
        # UI Components
        self._init_ui()
        self._load_categories()
        
        # Auto-search timer
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
    
    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Title
        title_label = QLabel("Advanced Document Search")
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        main_layout.addWidget(title_label)
        
        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Search controls
        self._create_search_panel(splitter)
        
        # Right panel - Results and preview
        self._create_results_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
    
    def _create_search_panel(self, parent):
        """Create the search controls panel."""
        search_widget = QWidget()
        search_layout = QVBoxLayout(search_widget)
        
        # Search Query Group
        query_group = QGroupBox("Search Query")
        query_layout = QFormLayout()
        
        # Main search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search terms...")
        self.search_input.textChanged.connect(self._on_search_text_changed)
        query_layout.addRow("Search:", self.search_input)
        
        # Search options
        self.case_sensitive = QCheckBox("Case sensitive")
        query_layout.addRow("", self.case_sensitive)
        
        self.whole_words = QCheckBox("Whole words only")
        query_layout.addRow("", self.whole_words)
        
        self.search_content = QCheckBox("Search in content")
        self.search_content.setChecked(True)
        query_layout.addRow("", self.search_content)
        
        query_group.setLayout(query_layout)
        search_layout.addWidget(query_group)
        
        # Filters Group
        filters_group = QGroupBox("Filters")
        filters_layout = QFormLayout()
        
        # Category filter
        self.category_combo = QComboBox()
        filters_layout.addRow("Category:", self.category_combo)
        
        # Document type filter
        self.type_combo = QComboBox()
        self.type_combo.addItem("All Types", None)
        for doc_type in DocumentType:
            self.type_combo.addItem(doc_type.value.upper(), doc_type)
        filters_layout.addRow("Type:", self.type_combo)
        
        # Content filters
        self.has_images = QCheckBox("Has images")
        filters_layout.addRow("", self.has_images)
        
        self.has_tables = QCheckBox("Has tables")
        filters_layout.addRow("", self.has_tables)
        
        filters_group.setLayout(filters_layout)
        search_layout.addWidget(filters_group)
        
        # Date Range Group
        date_group = QGroupBox("Date Range")
        date_layout = QFormLayout()
        
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(datetime.now().date())
        self.date_from.setEnabled(False)
        date_layout.addRow("From:", self.date_from)
        
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(datetime.now().date())
        self.date_to.setEnabled(False)
        date_layout.addRow("To:", self.date_to)
        
        self.use_date_filter = QCheckBox("Enable date filter")
        self.use_date_filter.toggled.connect(self._toggle_date_filter)
        date_layout.addRow("", self.use_date_filter)
        
        date_group.setLayout(date_layout)
        search_layout.addWidget(date_group)
        
        # Search button
        self.search_button = QPushButton("Search")
        self.search_button.clicked.connect(self._perform_search)
        search_layout.addWidget(self.search_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        search_layout.addWidget(self.progress_bar)
        
        # Statistics
        self.stats_label = QLabel("Ready to search...")
        self.stats_label.setStyleSheet("font-size: 10pt; color: gray;")
        search_layout.addWidget(self.stats_label)
        
        search_layout.addStretch()
        parent.addWidget(search_widget)
    
    def _create_results_panel(self, parent):
        """Create the results and preview panel."""
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        
        # Results header
        header_layout = QHBoxLayout()
        
        self.results_label = QLabel("No search performed")
        self.results_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(self.results_label)
        
        header_layout.addStretch()
        
        # Navigation controls
        self.prev_match_btn = QPushButton("◀ Previous")
        self.prev_match_btn.setEnabled(False)
        self.prev_match_btn.clicked.connect(self._previous_match)
        header_layout.addWidget(self.prev_match_btn)
        
        self.match_label = QLabel("0/0")
        header_layout.addWidget(self.match_label)
        
        self.next_match_btn = QPushButton("Next ▶")
        self.next_match_btn.setEnabled(False)
        self.next_match_btn.clicked.connect(self._next_match)
        header_layout.addWidget(self.next_match_btn)
        
        results_layout.addLayout(header_layout)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "Document", "Matches", "Type", "Category", "Modified"
        ])
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.itemSelectionChanged.connect(self._on_result_selected)
        self.results_table.setMaximumHeight(200)
        results_layout.addWidget(self.results_table)
        
        # Document preview
        preview_label = QLabel("Document Preview")
        preview_label.setStyleSheet("font-weight: bold;")
        results_layout.addWidget(preview_label)
        
        self.document_preview = UnicodeTextEdit()
        self.document_preview.setReadOnly(True)
        results_layout.addWidget(self.document_preview)
        
        parent.addWidget(results_widget)
    
    def _load_categories(self):
        """Load categories into the combo box."""
        self.category_combo.clear()
        self.category_combo.addItem("All Categories", None)
        
        try:
            categories = self.category_service.get_all_categories()
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        except Exception as e:
            logger.error(f"Error loading categories: {e}")
    
    def _toggle_date_filter(self, enabled: bool):
        """Toggle date filter controls."""
        self.date_from.setEnabled(enabled)
        self.date_to.setEnabled(enabled)
    
    def _on_search_text_changed(self):
        """Handle search text changes with debouncing."""
        self.search_timer.stop()
        if self.search_input.text().strip():
            self.search_timer.start(500)  # 500ms delay
    
    def _perform_search(self):
        """Perform the document search."""
        query = self.search_input.text().strip()
        
        if not query:
            self._clear_results()
            return
        
        try:
            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate
            self.search_button.setEnabled(False)
            
            # Prepare search parameters
            category = self.category_combo.currentData()
            doc_type = self.type_combo.currentData()
            
            date_from = None
            date_to = None
            if self.use_date_filter.isChecked():
                date_from = datetime.combine(self.date_from.date().toPython(), datetime.min.time())
                date_to = datetime.combine(self.date_to.date().toPython(), datetime.max.time())
            
            has_images = self.has_images.isChecked() if self.has_images.isChecked() else None
            has_tables = self.has_tables.isChecked() if self.has_tables.isChecked() else None
            
            # Perform search
            self.current_results = self.document_service.search_converted_documents(
                query=query,
                search_in_content=self.search_content.isChecked(),
                case_sensitive=self.case_sensitive.isChecked(),
                whole_words_only=self.whole_words.isChecked(),
                category=category,
                has_images=has_images,
                has_tables=has_tables,
                date_from=date_from,
                date_to=date_to,
                limit=100
            )
            
            # Display results
            self._display_results()
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            self.stats_label.setText(f"Search error: {str(e)}")
        finally:
            # Hide progress
            self.progress_bar.setVisible(False)
            self.search_button.setEnabled(True)
    
    def _display_results(self):
        """Display search results in the table."""
        self.results_table.setRowCount(len(self.current_results))
        
        total_matches = 0
        for row, (document, matches) in enumerate(self.current_results):
            total_matches += len(matches)
            
            # Document name
            name_item = QTableWidgetItem(document.name)
            name_item.setData(Qt.ItemDataRole.UserRole, document.id)
            self.results_table.setItem(row, 0, name_item)
            
            # Match count
            match_count = QTableWidgetItem(str(len(matches)))
            self.results_table.setItem(row, 1, match_count)
            
            # Document type
            doc_type = QTableWidgetItem(document.file_type.value.upper())
            self.results_table.setItem(row, 2, doc_type)
            
            # Category
            category = QTableWidgetItem(document.category or "None")
            self.results_table.setItem(row, 3, category)
            
            # Modified date
            modified = QTableWidgetItem(document.last_modified_date.strftime("%Y-%m-%d %H:%M"))
            self.results_table.setItem(row, 4, modified)
        
        # Update results label
        self.results_label.setText(f"Found {len(self.current_results)} documents with {total_matches} matches")
        self.stats_label.setText(f"Search completed: {len(self.current_results)} documents, {total_matches} total matches")
        
        # Resize columns
        self.results_table.resizeColumnsToContents()
        
        # Emit signal
        self.search_completed.emit(len(self.current_results))
    
    def _clear_results(self):
        """Clear search results."""
        self.current_results = []
        self.current_document = None
        self.current_matches = []
        self.current_match_index = -1
        
        self.results_table.setRowCount(0)
        self.document_preview.clear()
        self.results_label.setText("No search performed")
        self.stats_label.setText("Ready to search...")
        self._update_navigation_controls()
    
    def _on_result_selected(self):
        """Handle result selection."""
        selected_items = self.results_table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        document, matches = self.current_results[row]
        
        self.current_document = document
        self.current_matches = matches
        self.current_match_index = 0 if matches else -1
        
        # Load document preview
        self._load_document_preview()
        
        # Update navigation
        self._update_navigation_controls()
        
        # Emit signal
        self.document_selected.emit(document.id)
    
    def _load_document_preview(self):
        """Load document content into preview."""
        if not self.current_document:
            return

        # Load content with smart HTML rendering
        content = self.current_document.extracted_text or self.current_document.content or ""
        DocumentPreviewRenderer.set_preview_content_for_search(self.document_preview, content)

        # Highlight first match if available
        if self.current_matches and self.current_match_index >= 0:
            self._highlight_current_match()



    def _highlight_current_match(self):
        """Highlight the current match in the preview."""
        if not self.current_matches or self.current_match_index < 0:
            return
        
        match = self.current_matches[self.current_match_index]
        if match['field'] != 'content':
            return
        
        # Create highlight format
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(QColor(255, 255, 0, 100))  # Yellow highlight
        
        # Get cursor and move to position
        cursor = self.document_preview.textCursor()
        cursor.setPosition(match['position'])
        cursor.movePosition(
            QTextCursor.MoveOperation.Right,
            QTextCursor.MoveMode.KeepAnchor,
            match['length']
        )
        
        # Apply highlight
        cursor.setCharFormat(highlight_format)
        
        # Scroll to position
        self.document_preview.setTextCursor(cursor)
        self.document_preview.ensureCursorVisible()
    
    def _update_navigation_controls(self):
        """Update navigation control states."""
        has_matches = len(self.current_matches) > 0
        
        self.prev_match_btn.setEnabled(has_matches and self.current_match_index > 0)
        self.next_match_btn.setEnabled(has_matches and self.current_match_index < len(self.current_matches) - 1)
        
        if has_matches:
            self.match_label.setText(f"{self.current_match_index + 1}/{len(self.current_matches)}")
        else:
            self.match_label.setText("0/0")
    
    def _previous_match(self):
        """Navigate to previous match."""
        if self.current_match_index > 0:
            self.current_match_index -= 1
            self._highlight_current_match()
            self._update_navigation_controls()
    
    def _next_match(self):
        """Navigate to next match."""
        if self.current_match_index < len(self.current_matches) - 1:
            self.current_match_index += 1
            self._highlight_current_match()
            self._update_navigation_controls()
