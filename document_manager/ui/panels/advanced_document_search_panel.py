"""
Advanced Document Search Panel

This panel provides comprehensive search functionality for documents in the database,
including phrase search, position tracking, and result navigation.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple

from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor, QTextCursor
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QCheckBox, QComboBox, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QSpinBox, QDateEdit, QTabWidget,
    QProgressBar, QFrame, QScrollArea, QDialog, QDialogButtonBox
)

from loguru import logger

from document_manager.models.document import Document, DocumentType
from document_manager.services.document_service import DocumentService
from document_manager.services.category_service import CategoryService
from document_manager.ui.utils.document_preview_utils import DocumentPreviewRenderer
from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


class AdvancedDocumentSearchPanel(QWidget):
    """Advanced search panel for documents with full-text search and navigation."""
    
    # Signals
    document_selected = pyqtSignal(str)  # Document ID
    search_completed = pyqtSignal(int)   # Number of results
    
    def __init__(self, parent=None):
        """Initialize the advanced search panel."""
        super().__init__(parent)
        self.setWindowTitle("Advanced Document Search")
        
        # Services
        self.document_service = DocumentService()
        self.category_service = CategoryService()
        
        # State
        self.current_results: List[Tuple[Document, List[Dict]]] = []
        self.current_document: Optional[Document] = None
        self.current_matches: List[Dict] = []
        self.current_match_index: int = -1
        
        # UI Components
        self._init_ui()
        self._load_categories()
        

    
    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        main_layout.setSpacing(5)  # Reduced spacing
        
        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Search controls
        self._create_search_panel(splitter)
        
        # Right panel - Results and preview
        self._create_results_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
    
    def _create_search_panel(self, parent):
        """Create the search controls panel."""
        search_widget = QWidget()
        search_layout = QVBoxLayout(search_widget)
        
        # Search Query Group
        query_group = QGroupBox("Search Query")
        query_layout = QFormLayout()
        
        # Main search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search terms...")
        self.search_input.returnPressed.connect(self._perform_search)  # Search on Enter
        query_layout.addRow("Search:", self.search_input)
        
        # Search options
        self.case_sensitive = QCheckBox("Case sensitive")
        query_layout.addRow("", self.case_sensitive)
        
        self.whole_words = QCheckBox("Whole words only")
        query_layout.addRow("", self.whole_words)
        
        self.search_content = QCheckBox("Search in content")
        self.search_content.setChecked(True)
        query_layout.addRow("", self.search_content)
        
        query_group.setLayout(query_layout)
        search_layout.addWidget(query_group)
        
        # Filters Group
        filters_group = QGroupBox("Filters")
        filters_layout = QFormLayout()
        
        # Category filter
        self.category_combo = QComboBox()
        filters_layout.addRow("Category:", self.category_combo)
        
        # Document type filter
        self.type_combo = QComboBox()
        self.type_combo.addItem("All Types", None)
        for doc_type in DocumentType:
            self.type_combo.addItem(doc_type.value.upper(), doc_type)
        filters_layout.addRow("Type:", self.type_combo)
        
        # Content filters
        self.has_images = QCheckBox("Has images")
        filters_layout.addRow("", self.has_images)
        
        self.has_tables = QCheckBox("Has tables")
        filters_layout.addRow("", self.has_tables)
        
        filters_group.setLayout(filters_layout)
        search_layout.addWidget(filters_group)
        
        # Date Range Group
        date_group = QGroupBox("Date Range")
        date_layout = QFormLayout()
        
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(datetime.now().date())
        self.date_from.setEnabled(False)
        date_layout.addRow("From:", self.date_from)
        
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(datetime.now().date())
        self.date_to.setEnabled(False)
        date_layout.addRow("To:", self.date_to)
        
        self.use_date_filter = QCheckBox("Enable date filter")
        self.use_date_filter.toggled.connect(self._toggle_date_filter)
        date_layout.addRow("", self.use_date_filter)
        
        date_group.setLayout(date_layout)
        search_layout.addWidget(date_group)
        
        # Search button
        self.search_button = QPushButton("Search")
        self.search_button.clicked.connect(self._perform_search)
        search_layout.addWidget(self.search_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        search_layout.addWidget(self.progress_bar)
        
        # Statistics
        self.stats_label = QLabel("Ready to search...")
        self.stats_label.setStyleSheet("font-size: 10pt; color: gray;")
        search_layout.addWidget(self.stats_label)
        
        search_layout.addStretch()
        parent.addWidget(search_widget)
    
    def _create_results_panel(self, parent):
        """Create the results and preview panel."""
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        
        # Results header
        header_layout = QHBoxLayout()
        
        self.results_label = QLabel("No search performed")
        self.results_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(self.results_label)
        
        header_layout.addStretch()
        
        # Navigation controls
        self.prev_match_btn = QPushButton("◀ Previous")
        self.prev_match_btn.setEnabled(False)
        self.prev_match_btn.clicked.connect(self._previous_match)
        header_layout.addWidget(self.prev_match_btn)
        
        self.match_label = QLabel("0/0")
        header_layout.addWidget(self.match_label)
        
        self.next_match_btn = QPushButton("Next ▶")
        self.next_match_btn.setEnabled(False)
        self.next_match_btn.clicked.connect(self._next_match)
        header_layout.addWidget(self.next_match_btn)
        
        results_layout.addLayout(header_layout)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels([
            "Document", "Matches", "Preview", "Type", "Category", "Modified"
        ])
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.itemSelectionChanged.connect(self._on_result_selected)
        self.results_table.itemDoubleClicked.connect(self._on_result_double_clicked)
        self.results_table.setMaximumHeight(200)
        results_layout.addWidget(self.results_table)
        
        # Document preview
        preview_label = QLabel("Document Preview")
        preview_label.setStyleSheet("font-weight: bold;")
        results_layout.addWidget(preview_label)
        
        self.document_preview = UnicodeTextEdit()
        self.document_preview.setReadOnly(True)
        results_layout.addWidget(self.document_preview)
        
        parent.addWidget(results_widget)
    
    def _load_categories(self):
        """Load categories into the combo box."""
        self.category_combo.clear()
        self.category_combo.addItem("All Categories", None)
        
        try:
            categories = self.category_service.get_all_categories()
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        except Exception as e:
            logger.error(f"Error loading categories: {e}")
    
    def _toggle_date_filter(self, enabled: bool):
        """Toggle date filter controls."""
        self.date_from.setEnabled(enabled)
        self.date_to.setEnabled(enabled)
    

    
    def _perform_search(self):
        """Perform the document search."""
        query = self.search_input.text().strip()
        
        if not query:
            self._clear_results()
            return
        
        try:
            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate
            self.search_button.setEnabled(False)
            
            # Prepare search parameters
            category = self.category_combo.currentData()
            doc_type = self.type_combo.currentData()
            
            date_from = None
            date_to = None
            if self.use_date_filter.isChecked():
                date_from = datetime.combine(self.date_from.date().toPython(), datetime.min.time())
                date_to = datetime.combine(self.date_to.date().toPython(), datetime.max.time())
            
            has_images = self.has_images.isChecked() if self.has_images.isChecked() else None
            has_tables = self.has_tables.isChecked() if self.has_tables.isChecked() else None
            
            # Perform search
            self.current_results = self._search_documents_with_matches(
                query=query,
                search_in_content=self.search_content.isChecked(),
                case_sensitive=self.case_sensitive.isChecked(),
                whole_words_only=self.whole_words.isChecked(),
                category=category,
                date_from=date_from,
                date_to=date_to,
                limit=100
            )
            
            # Display results
            self._display_results()
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            self.stats_label.setText(f"Search error: {str(e)}")
        finally:
            # Hide progress
            self.progress_bar.setVisible(False)
            self.search_button.setEnabled(True)
    
    def _display_results(self):
        """Display search results in the table."""
        self.results_table.setRowCount(len(self.current_results))

        total_matches = 0
        for row, (document, matches) in enumerate(self.current_results):
            total_matches += len(matches)

            # Document name
            name_item = QTableWidgetItem(document.name)
            name_item.setData(Qt.ItemDataRole.UserRole, document.id)
            self.results_table.setItem(row, 0, name_item)

            # Match count
            match_count = QTableWidgetItem(str(len(matches)))
            self.results_table.setItem(row, 1, match_count)

            # Preview - show first match context (skip URI matches)
            preview_text = "No matches"
            if matches:
                # Find first non-URI match for preview
                first_content_match = None
                for match in matches:
                    if match['field'] == 'content' and not self._is_uri_match(match):
                        first_content_match = match
                        break

                if first_content_match and 'context' in first_content_match:
                    # Clean up the context and preserve some paragraph structure
                    context = first_content_match['context'].strip()
                    # Replace multiple newlines with single space, but preserve sentence breaks
                    context = context.replace('\n\n', ' | ').replace('\n', ' ')
                    # Remove any remaining URI fragments
                    context = self._clean_uri_fragments(context)

                    # If context is still mostly [DATA] or [IMAGE], skip it
                    if '[DATA]' in context or '[IMAGE]' in context:
                        # Try to find a better match
                        for match in matches[1:]:  # Skip the first one we already tried
                            if match['field'] == 'content' and not self._is_uri_match(match):
                                alt_context = match.get('context', '').strip()
                                # Preserve some paragraph structure
                                alt_context = alt_context.replace('\n\n', ' | ').replace('\n', ' ')
                                alt_context = self._clean_uri_fragments(alt_context)
                                if '[DATA]' not in alt_context and '[IMAGE]' not in alt_context and alt_context:
                                    context = alt_context
                                    break
                        else:
                            # If no clean context found, use document name
                            context = f"Found in document content"

                    if len(context) > 100:
                        context = context[:97] + "..."
                    preview_text = context
                elif matches[0]['field'] == 'name':
                    preview_text = f"Found in document name: {document.name}"

            preview_item = QTableWidgetItem(preview_text)
            preview_item.setToolTip(preview_text)  # Full text on hover
            self.results_table.setItem(row, 2, preview_item)

            # Document type
            doc_type = QTableWidgetItem(document.file_type.value.upper())
            self.results_table.setItem(row, 3, doc_type)

            # Category
            category = QTableWidgetItem(document.category or "None")
            self.results_table.setItem(row, 4, category)

            # Modified date
            modified = QTableWidgetItem(document.last_modified_date.strftime("%Y-%m-%d %H:%M"))
            self.results_table.setItem(row, 5, modified)
        
        # Update results label
        self.results_label.setText(f"Found {len(self.current_results)} documents with {total_matches} matches")
        self.stats_label.setText(f"Search completed: {len(self.current_results)} documents, {total_matches} total matches")
        
        # Resize columns
        self.results_table.resizeColumnsToContents()
        
        # Emit signal
        self.search_completed.emit(len(self.current_results))
    
    def _clear_results(self):
        """Clear search results."""
        self.current_results = []
        self.current_document = None
        self.current_matches = []
        self.current_match_index = -1
        
        self.results_table.setRowCount(0)
        self.document_preview.clear()
        self.results_label.setText("No search performed")
        self.stats_label.setText("Ready to search...")
        self._update_navigation_controls()
    
    def _on_result_selected(self):
        """Handle result selection."""
        selected_items = self.results_table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        document, matches = self.current_results[row]
        
        self.current_document = document
        self.current_matches = matches
        self.current_match_index = 0 if matches else -1
        
        # Load document preview
        self._load_document_preview()
        
        # Update navigation
        self._update_navigation_controls()
        
        # Emit signal
        self.document_selected.emit(document.id)
    
    def _load_document_preview(self):
        """Load document content into preview."""
        if not self.current_document:
            return

        # Load content with smart HTML rendering
        content = self.current_document.extracted_text or self.current_document.content or ""
        DocumentPreviewRenderer.set_preview_content_for_search(self.document_preview, content)

        # Highlight first match if available
        if self.current_matches and self.current_match_index >= 0:
            self._highlight_current_match()



    def _highlight_current_match(self):
        """Highlight the current match in the preview."""
        if not self.current_matches or self.current_match_index < 0:
            return

        match = self.current_matches[self.current_match_index]
        if match['field'] != 'content':
            return

        # Use text search instead of position-based navigation for better reliability
        search_text = match.get('text', '')
        if search_text and len(search_text) > 1:
            # Clear previous selections
            cursor = self.document_preview.textCursor()
            cursor.clearSelection()
            self.document_preview.setTextCursor(cursor)

            # Move to start and search for the nth occurrence
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            self.document_preview.setTextCursor(cursor)

            # Find the specific occurrence we want
            found_count = 0
            target_occurrence = self.current_match_index + 1

            # Count how many times this text appears before our target
            for i, m in enumerate(self.current_matches):
                if i >= self.current_match_index:
                    break
                if m.get('text') == search_text and m['field'] == 'content':
                    found_count += 1

            # Now find the (found_count + 1)th occurrence
            current_count = 0
            while current_count <= found_count:
                found = self.document_preview.find(search_text)
                if found:
                    current_count += 1
                    if current_count > found_count:
                        # This is our target occurrence
                        self.document_preview.ensureCursorVisible()

                        # Highlight it
                        cursor = self.document_preview.textCursor()
                        if cursor.hasSelection():
                            highlight_format = QTextCharFormat()
                            highlight_format.setBackground(QColor(255, 255, 0, 150))
                            cursor.setCharFormat(highlight_format)
                        break
                else:
                    break

    def _scroll_to_approximate_match(self, match: Dict):
        """Scroll to approximate match location when exact positioning fails."""
        try:
            # Try to find the text in the current document
            search_text = match.get('text', '')
            if search_text and len(search_text) > 2:  # Only search for meaningful text
                cursor = self.document_preview.textCursor()
                cursor.movePosition(QTextCursor.MoveOperation.Start)

                # Search for the text multiple times to find the right occurrence
                found_count = 0
                target_occurrence = getattr(self, 'current_match_index', 0) + 1

                while found_count < target_occurrence:
                    found = self.document_preview.find(search_text)
                    if found:
                        found_count += 1
                        if found_count == target_occurrence:
                            self.document_preview.ensureCursorVisible()
                            # Highlight the found text
                            cursor = self.document_preview.textCursor()
                            if cursor.hasSelection():
                                highlight_format = QTextCharFormat()
                                highlight_format.setBackground(QColor(255, 255, 0, 150))
                                cursor.setCharFormat(highlight_format)
                            break
                    else:
                        break

                if found_count == 0:
                    logger.debug(f"Could not find text '{search_text}' in document preview")
        except Exception as e:
            logger.debug(f"Could not scroll to approximate match: {e}")
    
    def _update_navigation_controls(self):
        """Update navigation control states."""
        has_matches = len(self.current_matches) > 0
        
        self.prev_match_btn.setEnabled(has_matches and self.current_match_index > 0)
        self.next_match_btn.setEnabled(has_matches and self.current_match_index < len(self.current_matches) - 1)
        
        if has_matches:
            self.match_label.setText(f"{self.current_match_index + 1}/{len(self.current_matches)}")
        else:
            self.match_label.setText("0/0")
    
    def _previous_match(self):
        """Navigate to previous match."""
        if self.current_match_index > 0:
            self.current_match_index -= 1
            self._highlight_current_match()
            self._update_navigation_controls()
    
    def _next_match(self):
        """Navigate to next match."""
        if self.current_match_index < len(self.current_matches) - 1:
            self.current_match_index += 1
            self._highlight_current_match()
            self._update_navigation_controls()

    def _on_result_double_clicked(self, item):
        """Handle double-click on search result to open document viewer."""
        if not item:
            return

        row = item.row()
        if row >= len(self.current_results):
            return

        document, matches = self.current_results[row]

        # Open document viewer dialog
        self._open_document_viewer(document, matches)

    def _open_document_viewer(self, document: Document, matches: List[Dict]):
        """Open a document viewer dialog with search highlighting."""
        try:
            from document_manager.ui.dialogs.document_search_viewer_dialog import DocumentSearchViewerDialog

            dialog = DocumentSearchViewerDialog(
                document=document,
                matches=matches,
                search_query=self.search_input.text(),
                parent=self
            )
            dialog.exec()

        except ImportError:
            # Fallback: Create a simple viewer dialog
            self._create_simple_document_viewer(document, matches)
        except Exception as e:
            logger.error(f"Error opening document viewer: {e}")
            # Fallback to simple viewer
            self._create_simple_document_viewer(document, matches)

    def _create_simple_document_viewer(self, document: Document, matches: List[Dict]):
        """Create a simple document viewer dialog as fallback."""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Document Viewer - {document.name}")
        dialog.setModal(True)
        dialog.resize(800, 600)

        layout = QVBoxLayout(dialog)

        # Document info
        info_label = QLabel(f"Document: {document.name} | Matches: {len(matches)}")
        info_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(info_label)

        # Document content
        content_widget = UnicodeTextEdit()
        content_widget.setReadOnly(True)

        # Load content with highlighting
        content = document.extracted_text or document.content or "No content available"
        DocumentPreviewRenderer.set_preview_content_for_viewer(content_widget, content)

        # Highlight all matches
        self._highlight_all_matches_in_viewer(content_widget, matches, self.search_input.text())

        layout.addWidget(content_widget)

        # Navigation controls
        nav_layout = QHBoxLayout()

        if matches:
            prev_btn = QPushButton("Previous Match")
            next_btn = QPushButton("Next Match")
            match_label = QLabel(f"1/{len(matches)}")

            nav_layout.addWidget(prev_btn)
            nav_layout.addWidget(match_label)
            nav_layout.addWidget(next_btn)
            nav_layout.addStretch()

            # Store state for navigation
            dialog.current_match_index = 0
            dialog.matches = matches
            dialog.content_widget = content_widget
            dialog.match_label = match_label

            # Connect navigation
            prev_btn.clicked.connect(lambda: self._navigate_viewer_match(dialog, -1))
            next_btn.clicked.connect(lambda: self._navigate_viewer_match(dialog, 1))

            # Navigate to first match
            self._navigate_to_viewer_match(dialog, 0)

        layout.addLayout(nav_layout)

        # Close button
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        dialog.exec()

    def _highlight_all_matches_in_viewer(self, text_widget: UnicodeTextEdit, matches: List[Dict], query: str):
        """Highlight all matches in the document viewer."""
        if not matches:
            return

        # Create highlight format
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(QColor(255, 255, 0, 100))  # Yellow highlight

        cursor = text_widget.textCursor()
        document_length = len(text_widget.toPlainText())

        for match in matches:
            if match['field'] == 'content':
                try:
                    # Ensure position is within bounds
                    safe_position = min(match['position'], document_length - 1)
                    safe_length = min(match['length'], document_length - safe_position)

                    if safe_position >= 0 and safe_length > 0:
                        cursor.setPosition(safe_position)
                        cursor.movePosition(
                            QTextCursor.MoveOperation.Right,
                            QTextCursor.MoveMode.KeepAnchor,
                            safe_length
                        )
                        cursor.setCharFormat(highlight_format)
                except Exception as e:
                    logger.debug(f"Could not highlight match at position {match['position']}: {e}")
                    continue

    def _navigate_viewer_match(self, dialog, direction: int):
        """Navigate between matches in the viewer dialog."""
        if not hasattr(dialog, 'matches') or not dialog.matches:
            return

        new_index = dialog.current_match_index + direction
        if 0 <= new_index < len(dialog.matches):
            dialog.current_match_index = new_index
            self._navigate_to_viewer_match(dialog, new_index)

    def _navigate_to_viewer_match(self, dialog, match_index: int):
        """Navigate to a specific match in the viewer dialog."""
        if not hasattr(dialog, 'matches') or match_index >= len(dialog.matches):
            return

        match = dialog.matches[match_index]
        if match['field'] != 'content':
            return

        # Update label
        dialog.match_label.setText(f"{match_index + 1}/{len(dialog.matches)}")

        # Navigate to position safely
        try:
            cursor = dialog.content_widget.textCursor()
            document_length = len(dialog.content_widget.toPlainText())

            # Ensure position is within bounds
            safe_position = min(match['position'], document_length - 1)

            if safe_position >= 0:
                cursor.setPosition(safe_position)
                dialog.content_widget.setTextCursor(cursor)
                dialog.content_widget.ensureCursorVisible()
        except Exception as e:
            logger.debug(f"Could not navigate to match position {match['position']}: {e}")
            # Fallback: try to find the text
            try:
                search_text = match.get('text', '')
                if search_text:
                    cursor = dialog.content_widget.textCursor()
                    cursor.movePosition(QTextCursor.MoveOperation.Start)
                    found = dialog.content_widget.find(search_text)
                    if found:
                        dialog.content_widget.ensureCursorVisible()
            except Exception as e2:
                logger.debug(f"Could not find text fallback: {e2}")

    def _search_documents_with_matches(
        self,
        query: str,
        search_in_content: bool = True,
        case_sensitive: bool = False,
        whole_words_only: bool = False,
        category: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Tuple[Document, List[Dict]]]:
        """Search for documents with text matching and position tracking.

        Args:
            query: Search text
            search_in_content: Whether to search in document content
            case_sensitive: Whether search should be case sensitive
            whole_words_only: Whether to match whole words only
            category: Filter by category
            date_from: Filter by creation date (from)
            date_to: Filter by creation date (to)
            limit: Maximum number of results

        Returns:
            List of tuples (Document, list of match positions)
        """
        try:
            # Get all documents first
            all_documents = self.document_service.search_documents(
                query="",  # Empty query to get all documents
                category=category,
                date_from=date_from,
                date_to=date_to
            )

            logger.debug(f"Found {len(all_documents)} total documents for search")

            # If no query, return all documents
            if not query:
                return [(doc, []) for doc in all_documents[:limit]]

            # Perform text search with position tracking
            results = []
            for document in all_documents:
                matches = self._find_text_matches(
                    document,
                    query,
                    search_in_content,
                    case_sensitive,
                    whole_words_only
                )

                # Only include documents that have meaningful (non-URI) matches
                meaningful_matches = [m for m in matches if not self._is_uri_match(m)]

                if meaningful_matches:
                    logger.debug(f"Found {len(meaningful_matches)} meaningful matches in document: {document.name}")
                    results.append((document, meaningful_matches))
                elif matches:
                    logger.debug(f"Skipping document {document.name}: {len(matches)} matches were all in URIs/data")

                if len(results) >= limit:
                    break

            logger.debug(f"Search completed: {len(results)} documents with meaningful matches")
            return results

        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []

    def _find_text_matches(
        self,
        document: Document,
        query: str,
        search_in_content: bool,
        case_sensitive: bool,
        whole_words_only: bool
    ) -> List[Dict]:
        """Find text matches in a document with position information.

        Args:
            document: Document to search
            query: Search query
            search_in_content: Whether to search in content
            case_sensitive: Whether search is case sensitive
            whole_words_only: Whether to match whole words only

        Returns:
            List of match dictionaries with position information
        """
        import re

        matches = []

        # Prepare search text
        search_text = query if case_sensitive else query.lower()

        # Search in document name
        doc_name = document.name if case_sensitive else document.name.lower()
        if search_text in doc_name:
            matches.append({
                'field': 'name',
                'text': document.name,
                'position': doc_name.find(search_text),
                'length': len(query),
                'context': document.name
            })

        # Search in content if requested
        content_to_search = None
        if search_in_content:
            # Try extracted_text first, then content, then notes
            if document.extracted_text:
                content_to_search = document.extracted_text
                logger.debug(f"Searching in extracted_text for {document.name} ({len(content_to_search)} chars)")
            elif document.content:
                content_to_search = document.content
                logger.debug(f"Searching in content for {document.name} ({len(content_to_search)} chars)")
            elif document.notes:
                content_to_search = document.notes
                logger.debug(f"Searching in notes for {document.name} ({len(content_to_search)} chars)")
            else:
                logger.debug(f"No searchable content found for {document.name}")

        if content_to_search:
            content = content_to_search if case_sensitive else content_to_search.lower()

            if whole_words_only:
                # Use regex for whole word matching
                pattern = r'\b' + re.escape(search_text) + r'\b'
                flags = 0 if case_sensitive else re.IGNORECASE

                for match in re.finditer(pattern, content, flags):
                    # Get context around the match
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]

                    matches.append({
                        'field': 'content',
                        'text': match.group(),
                        'position': match.start(),
                        'length': len(match.group()),
                        'context': context,
                        'context_start': start
                    })
            else:
                # Simple substring search
                start = 0
                while True:
                    pos = content.find(search_text, start)
                    if pos == -1:
                        break

                    # Get context around the match
                    context_start = max(0, pos - 50)
                    context_end = min(len(content), pos + len(search_text) + 50)
                    context = content[context_start:context_end]

                    matches.append({
                        'field': 'content',
                        'text': search_text,
                        'position': pos,
                        'length': len(search_text),
                        'context': context,
                        'context_start': context_start
                    })

                    start = pos + 1

        return matches

    def _is_uri_match(self, match: Dict) -> bool:
        """Check if a match occurs within a URI (base64 data URI, etc.)."""
        context = match.get('context', '')

        # Check for data URI patterns
        uri_patterns = [
            'data:image/',
            'data:application/',
            'src="data:',
            'href="data:',
            'base64,',
            'data:text/html;charset=utf-8;base64,'
        ]

        # Check if the match context contains URI patterns
        context_lower = context.lower()
        for pattern in uri_patterns:
            if pattern in context_lower:
                return True

        # Check if the match position is within a long base64-like string
        if len(context) > 200 and not any(c in context for c in [' ', '\n', '\t']):
            # Likely a base64 string
            return True

        return False

    def _clean_uri_fragments(self, text: str) -> str:
        """Remove URI fragments from text for cleaner display."""
        import re

        # Remove data URIs
        text = re.sub(r'data:[^;]+;base64,[A-Za-z0-9+/=]+', '[IMAGE]', text)
        text = re.sub(r'data:image/[^;]+;base64,[A-Za-z0-9+/=]+', '[IMAGE]', text)
        text = re.sub(r'src="data:[^"]*"', 'src="[IMAGE]"', text)

        # Remove long base64-like strings
        text = re.sub(r'[A-Za-z0-9+/=]{50,}', '[DATA]', text)

        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text)

        return text.strip()
