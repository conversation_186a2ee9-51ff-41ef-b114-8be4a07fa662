"""
Simple Knowledge Base Panel

A simplified Knowledge Base interface that integrates with the existing
document management system while providing knowledge base functionality.
"""

from datetime import datetime
from typing import Dict, List, Optional

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTextEdit, QListWidget, QListWidgetItem, QSplitter, QGroupBox,
    QFormLayout, QComboBox, QTabWidget, QMessageBox,
    QCheckBox
)

from loguru import logger

from document_manager.models.document import Document, DocumentType
from document_manager.services.document_service import DocumentService
from document_manager.services.category_service import CategoryService
from document_manager.ui.utils.document_preview_utils import DocumentPreviewRenderer


class SimpleKnowledgeBasePanel(QWidget):
    """Simplified Knowledge Base panel using existing document infrastructure."""
    
    # Signals
    document_selected = pyqtSignal(str)  # Document ID
    
    def __init__(self, parent=None):
        """Initialize the Knowledge Base panel."""
        super().__init__(parent)
        self.setWindowTitle("Knowledge Base")
        
        # Services
        self.document_service = DocumentService()
        self.category_service = CategoryService()

        # State
        self.current_documents: List[Document] = []
        self.current_document: Optional[Document] = None
        
        # UI
        self._init_ui()
        self._load_initial_data()
    
    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Title
        title_label = QLabel("📚 Knowledge Base")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50;")
        main_layout.addWidget(title_label)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_browse_tab()
        self._create_create_tab()
        self._create_organize_tab()
        self._create_search_tab()
        self._create_stats_tab()
    
    def _create_browse_tab(self):
        """Create the browse documents tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        # Category filter
        self.category_filter = QComboBox()
        self.category_filter.currentTextChanged.connect(self._filter_documents)
        controls_layout.addWidget(QLabel("Category:"))
        controls_layout.addWidget(self.category_filter)
        
        # Type filter
        self.type_filter = QComboBox()
        self.type_filter.addItem("All Types", None)
        for doc_type in DocumentType:
            self.type_filter.addItem(doc_type.value.upper(), doc_type)
        self.type_filter.currentTextChanged.connect(self._filter_documents)
        controls_layout.addWidget(QLabel("Type:"))
        controls_layout.addWidget(self.type_filter)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self._load_documents)
        controls_layout.addWidget(refresh_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Splitter for list and preview
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Document list
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        
        list_layout.addWidget(QLabel("Documents:"))
        self.document_list = QListWidget()
        self.document_list.itemSelectionChanged.connect(self._on_document_selected)
        list_layout.addWidget(self.document_list)
        
        # Document count
        self.doc_count_label = QLabel("0 documents")
        list_layout.addWidget(self.doc_count_label)
        
        splitter.addWidget(list_widget)
        
        # Document preview
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        
        preview_layout.addWidget(QLabel("Document Preview:"))
        self.document_preview = QTextEdit()
        self.document_preview.setReadOnly(True)
        preview_layout.addWidget(self.document_preview)
        
        # Document info
        self.doc_info_label = QLabel("Select a document to view details")
        self.doc_info_label.setStyleSheet("font-size: 10pt; color: gray;")
        preview_layout.addWidget(self.doc_info_label)
        
        splitter.addWidget(preview_widget)
        splitter.setSizes([300, 500])
        
        self.tab_widget.addTab(tab, "Browse")
    
    def _create_create_tab(self):
        """Create the document creation tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Form for new document
        form_group = QGroupBox("Create New Knowledge Base Document")
        form_layout = QFormLayout()
        
        # Title
        self.new_title = QLineEdit()
        self.new_title.setPlaceholderText("Enter document title...")
        form_layout.addRow("Title:", self.new_title)
        
        # Category
        self.new_category = QComboBox()
        form_layout.addRow("Category:", self.new_category)
        
        # Tags
        self.new_tags = QLineEdit()
        self.new_tags.setPlaceholderText("Enter tags separated by commas...")
        form_layout.addRow("Tags:", self.new_tags)
        
        # Notes
        self.new_notes = QLineEdit()
        self.new_notes.setPlaceholderText("Brief description or notes...")
        form_layout.addRow("Notes:", self.new_notes)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # Content editor
        content_group = QGroupBox("Document Content")
        content_layout = QVBoxLayout()
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        # Format buttons
        bold_btn = QPushButton("Bold")
        bold_btn.clicked.connect(lambda: self._format_text("bold"))
        toolbar_layout.addWidget(bold_btn)
        
        italic_btn = QPushButton("Italic")
        italic_btn.clicked.connect(lambda: self._format_text("italic"))
        toolbar_layout.addWidget(italic_btn)
        
        heading_btn = QPushButton("Heading")
        heading_btn.clicked.connect(lambda: self._format_text("heading"))
        toolbar_layout.addWidget(heading_btn)
        
        toolbar_layout.addStretch()
        content_layout.addLayout(toolbar_layout)
        
        # Content editor
        self.content_editor = QTextEdit()
        self.content_editor.setPlaceholderText("Enter your knowledge base content here...")
        content_layout.addWidget(self.content_editor)
        
        content_group.setLayout(content_layout)
        layout.addWidget(content_group)
        
        # Create button
        create_btn = QPushButton("Create Document")
        create_btn.clicked.connect(self._create_document)
        create_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; padding: 8px; }")
        layout.addWidget(create_btn)
        
        self.tab_widget.addTab(tab, "Create")
    
    def _create_organize_tab(self):
        """Create the organization tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Category management
        cat_group = QGroupBox("Category Management")
        cat_layout = QVBoxLayout()
        
        # New category
        new_cat_layout = QHBoxLayout()
        self.new_category_name = QLineEdit()
        self.new_category_name.setPlaceholderText("New category name...")
        new_cat_layout.addWidget(self.new_category_name)
        
        add_cat_btn = QPushButton("Add Category")
        add_cat_btn.clicked.connect(self._add_category)
        new_cat_layout.addWidget(add_cat_btn)
        
        cat_layout.addLayout(new_cat_layout)
        
        # Category list
        self.category_list = QListWidget()
        cat_layout.addWidget(self.category_list)
        
        cat_group.setLayout(cat_layout)
        layout.addWidget(cat_group)
        
        # Document organization
        org_group = QGroupBox("Document Organization")
        org_layout = QVBoxLayout()
        
        # Bulk operations
        bulk_layout = QHBoxLayout()
        
        bulk_category_btn = QPushButton("Bulk Categorize")
        bulk_category_btn.clicked.connect(self._bulk_categorize)
        bulk_layout.addWidget(bulk_category_btn)
        
        bulk_tag_btn = QPushButton("Bulk Tag")
        bulk_tag_btn.clicked.connect(self._bulk_tag)
        bulk_layout.addWidget(bulk_tag_btn)
        
        cleanup_btn = QPushButton("Cleanup Duplicates")
        cleanup_btn.clicked.connect(self._cleanup_duplicates)
        bulk_layout.addWidget(cleanup_btn)
        
        bulk_layout.addStretch()
        org_layout.addLayout(bulk_layout)
        
        org_group.setLayout(org_layout)
        layout.addWidget(org_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Organize")
    
    def _create_search_tab(self):
        """Create the search tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Search controls
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search knowledge base...")
        self.search_input.returnPressed.connect(self._perform_search)
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("Search")
        search_btn.clicked.connect(self._perform_search)
        search_layout.addWidget(search_btn)
        
        layout.addLayout(search_layout)
        
        # Search options
        options_layout = QHBoxLayout()
        
        self.search_content = QCheckBox("Search content")
        self.search_content.setChecked(True)
        options_layout.addWidget(self.search_content)
        
        self.case_sensitive = QCheckBox("Case sensitive")
        options_layout.addWidget(self.case_sensitive)
        
        options_layout.addStretch()
        layout.addLayout(options_layout)
        
        # Search results
        self.search_results = QListWidget()
        self.search_results.itemSelectionChanged.connect(self._on_search_result_selected)
        layout.addWidget(self.search_results)
        
        # Results info
        self.search_info_label = QLabel("Enter search terms above")
        layout.addWidget(self.search_info_label)
        
        self.tab_widget.addTab(tab, "Search")
    
    def _create_stats_tab(self):
        """Create the statistics tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statistics display
        stats_group = QGroupBox("Knowledge Base Statistics")
        stats_layout = QFormLayout()
        
        self.total_docs_label = QLabel("0")
        stats_layout.addRow("Total Documents:", self.total_docs_label)
        
        self.total_words_label = QLabel("0")
        stats_layout.addRow("Total Words:", self.total_words_label)
        
        self.categories_label = QLabel("0")
        stats_layout.addRow("Categories:", self.categories_label)
        
        self.avg_doc_size_label = QLabel("0")
        stats_layout.addRow("Average Document Size:", self.avg_doc_size_label)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # Refresh stats button
        refresh_stats_btn = QPushButton("Refresh Statistics")
        refresh_stats_btn.clicked.connect(self._update_statistics)
        layout.addWidget(refresh_stats_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Statistics")
    
    def _load_initial_data(self):
        """Load initial data."""
        self._load_categories()
        self._load_documents()
        self._update_statistics()
    
    def _load_categories(self):
        """Load categories into combo boxes."""
        try:
            categories = self.category_service.get_all_categories()

            # Update category filter
            self.category_filter.clear()
            self.category_filter.addItem("All Categories", None)
            for category in categories:
                self.category_filter.addItem(category.name, category.id)

            # Update new document category
            self.new_category.clear()
            self.new_category.addItem("No Category", None)
            for category in categories:
                self.new_category.addItem(category.name, category.id)

            # Update category list
            self.category_list.clear()
            for category in categories:
                # Get document count for this category
                try:
                    docs_in_category = self.document_service.search_documents()
                    # Filter by category manually since the service doesn't support category parameter
                    docs_in_category = [doc for doc in docs_in_category if doc.category == category.id]
                    doc_count = len(docs_in_category)
                except:
                    doc_count = 0

                item = QListWidgetItem(f"{category.name} ({doc_count} docs)")
                item.setData(Qt.ItemDataRole.UserRole, category.id)
                self.category_list.addItem(item)

            self.categories_label.setText(str(len(categories)))

        except Exception as e:
            logger.error(f"Error loading categories: {e}")
    
    def _load_documents(self):
        """Load documents from the database."""
        try:
            documents = self.document_service.search_documents()
            self.current_documents = documents

            self._update_document_list()
            self.total_docs_label.setText(str(len(documents)))

        except Exception as e:
            logger.error(f"Error loading documents: {e}")
    
    def _update_document_list(self):
        """Update the document list display."""
        self.document_list.clear()
        
        # Apply filters
        filtered_docs = self.current_documents
        
        # Category filter
        category_filter = self.category_filter.currentData()
        if category_filter:
            filtered_docs = [doc for doc in filtered_docs if doc.category == category_filter]
        
        # Type filter
        type_filter = self.type_filter.currentData()
        if type_filter:
            filtered_docs = [doc for doc in filtered_docs if doc.file_type == type_filter]
        
        # Add to list
        for document in filtered_docs:
            item_text = f"{document.name}"
            if document.word_count:
                item_text += f" ({document.word_count:,} words)"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, document.id)
            item.setToolTip(f"Category: {document.category or 'None'}\nModified: {document.last_modified_date}")
            self.document_list.addItem(item)
        
        self.doc_count_label.setText(f"{len(filtered_docs)} documents")
    
    def _filter_documents(self):
        """Filter documents based on current filters."""
        self._update_document_list()
    
    def _on_document_selected(self):
        """Handle document selection."""
        selected_items = self.document_list.selectedItems()
        if not selected_items:
            return
        
        document_id = selected_items[0].data(Qt.ItemDataRole.UserRole)
        document = next((doc for doc in self.current_documents if doc.id == document_id), None)
        
        if document:
            self.current_document = document

            # Update preview using shared renderer
            content = document.content or document.extracted_text or "No content available"
            DocumentPreviewRenderer.set_preview_content_for_search(
                self.document_preview,
                content
            )
            
            # Update info
            info_text = f"Title: {document.name}\n"
            info_text += f"Category: {document.category or 'None'}\n"
            info_text += f"Type: {document.file_type.value.upper()}\n"
            info_text += f"Size: {document.size_bytes:,} bytes\n"
            info_text += f"Words: {document.word_count or 0:,}\n"
            info_text += f"Created: {document.creation_date}\n"
            info_text += f"Modified: {document.last_modified_date}"
            
            self.doc_info_label.setText(info_text)
            
            # Emit signal
            self.document_selected.emit(document_id)
    
    def _format_text(self, format_type: str):
        """Apply text formatting."""
        cursor = self.content_editor.textCursor()
        if not cursor.hasSelection():
            return
        
        selected_text = cursor.selectedText()
        
        if format_type == "bold":
            formatted_text = f"**{selected_text}**"
        elif format_type == "italic":
            formatted_text = f"*{selected_text}*"
        elif format_type == "heading":
            formatted_text = f"# {selected_text}"
        else:
            return
        
        cursor.insertText(formatted_text)
    
    def _create_document(self):
        """Create a new document."""
        title = self.new_title.text().strip()
        if not title:
            QMessageBox.warning(self, "Error", "Please enter a document title.")
            return
        
        try:
            # Get form data
            category = self.new_category.currentData()
            tags_text = self.new_tags.text().strip()
            tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []
            notes = self.new_notes.text().strip() or None
            content = self.content_editor.toPlainText()
            
            # Create document using document service
            from document_manager.models.document import Document
            import uuid
            from pathlib import Path
            
            document = Document(
                id=str(uuid.uuid4()),
                name=title,
                file_path=Path(f"knowledge_base/{title}.kb"),
                file_type=DocumentType.TXT,  # Knowledge base documents
                size_bytes=len(content.encode('utf-8')),
                creation_date=datetime.now(),
                last_modified_date=datetime.now(),
                content=content,
                extracted_text=content,
                tags=set(tags),
                category=category,
                notes=notes,
                word_count=len(content.split()),
                metadata={'knowledge_base_document': True}
            )
            
            # Save document
            saved_doc = self.document_service.save_document(document)
            if saved_doc:
                QMessageBox.information(self, "Success", f"Document '{title}' created successfully!")
                
                # Clear form
                self.new_title.clear()
                self.new_tags.clear()
                self.new_notes.clear()
                self.content_editor.clear()
                
                # Refresh document list
                self._load_documents()
                
                # Switch to browse tab
                self.tab_widget.setCurrentIndex(0)
            else:
                QMessageBox.critical(self, "Error", "Failed to save document.")
                
        except Exception as e:
            logger.error(f"Error creating document: {e}")
            QMessageBox.critical(self, "Error", f"Error creating document:\n{str(e)}")
    
    def _add_category(self):
        """Add a new category."""
        name = self.new_category_name.text().strip()
        if not name:
            QMessageBox.warning(self, "Error", "Please enter a category name.")
            return
        
        try:
            category = self.category_service.create_category(name)
            if category:
                QMessageBox.information(self, "Success", f"Category '{name}' created successfully!")
                self.new_category_name.clear()
                self._load_categories()
            else:
                QMessageBox.critical(self, "Error", "Failed to create category.")
                
        except Exception as e:
            logger.error(f"Error creating category: {e}")
            QMessageBox.critical(self, "Error", f"Error creating category:\n{str(e)}")
    
    def _bulk_categorize(self):
        """Bulk categorize documents."""
        QMessageBox.information(self, "Feature", "Bulk categorization feature coming soon!")
    
    def _bulk_tag(self):
        """Bulk tag documents."""
        QMessageBox.information(self, "Feature", "Bulk tagging feature coming soon!")
    
    def _cleanup_duplicates(self):
        """Clean up duplicate documents."""
        QMessageBox.information(self, "Feature", "Duplicate cleanup feature coming soon!")
    
    def _perform_search(self):
        """Perform search in knowledge base."""
        query = self.search_input.text().strip()
        if not query:
            return
        
        try:
            # Search using document service
            documents = self.document_service.search_documents(query=query)
            
            # Filter for knowledge base documents
            kb_documents = [
                doc for doc in documents 
                if doc.metadata and doc.metadata.get('knowledge_base_document', False)
            ]
            
            # Update search results
            self.search_results.clear()
            for document in kb_documents:
                item = QListWidgetItem(f"{document.name} ({document.word_count or 0} words)")
                item.setData(Qt.ItemDataRole.UserRole, document.id)
                item.setToolTip(f"Category: {document.category or 'None'}")
                self.search_results.addItem(item)
            
            self.search_info_label.setText(f"Found {len(kb_documents)} knowledge base documents")
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            self.search_info_label.setText(f"Search error: {str(e)}")
    
    def _on_search_result_selected(self):
        """Handle search result selection."""
        selected_items = self.search_results.selectedItems()
        if not selected_items:
            return
        
        document_id = selected_items[0].data(Qt.ItemDataRole.UserRole)
        self.document_selected.emit(document_id)
        
        # Switch to browse tab and select the document
        self.tab_widget.setCurrentIndex(0)
        # Find and select the document in the list
        for i in range(self.document_list.count()):
            item = self.document_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == document_id:
                self.document_list.setCurrentItem(item)
                break
    
    def _update_statistics(self):
        """Update statistics display."""
        try:
            # Get all documents
            documents = self.document_service.search_documents()
            
            # Filter for knowledge base documents
            kb_documents = [
                doc for doc in documents 
                if doc.metadata and doc.metadata.get('knowledge_base_document', False)
            ]
            
            # Calculate statistics
            total_words = sum(doc.word_count or 0 for doc in kb_documents)
            avg_size = sum(doc.size_bytes for doc in kb_documents) / len(kb_documents) if kb_documents else 0
            
            # Update labels
            self.total_docs_label.setText(str(len(kb_documents)))
            self.total_words_label.setText(f"{total_words:,}")
            self.avg_doc_size_label.setText(f"{avg_size:,.0f} bytes")
            
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
