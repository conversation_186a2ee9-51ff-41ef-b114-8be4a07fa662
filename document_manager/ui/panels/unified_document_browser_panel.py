"""
Purpose: Provides a unified document browser panel that consolidates document management functionality.

This file is part of the document_manager pillar and serves as a UI component.
It combines the functionality of DocumentManagerPanel, DocumentBrowserPanel, and parts of 
DocumentDatabaseManagerPanel into a single, comprehensive document management interface.

Key components:
- UnifiedDocumentBrowserPanel: Unified panel for all document management operations

Dependencies:
- PyQt6: For UI components
- document_manager.models.document: For Document model
- document_manager.services.document_service: For document operations
- document_manager.services.category_service: For category operations
"""

from pathlib import Path
from typing import Optional

from loguru import logger
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDialog,
    QDialogButtonBox,
    QFileDialog,
    QHBoxLayout,
    QInputDialog,
    QLabel,
    QLineEdit,
    QMenu,
    Q<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Q<PERSON>reeW<PERSON>t,
    QTreeWidgetItem,
    QVBoxLayout,
    QWidget,
)

from document_manager.models.document import DocumentType
from document_manager.services.category_service import CategoryService
from document_manager.services.document_service import DocumentService
from document_manager.services.document_state_manager import get_state_manager
from document_manager.ui.dialogs.category_manager_dialog import CategoryManagerDialog
from document_manager.ui.dialogs.document_viewer_dialog import DocumentViewerDialog
from document_manager.ui.utils.document_preview_utils import DocumentPreviewRenderer
from document_manager.ui.widgets.cross_component_actions import CrossComponentActionBar
from shared.ui.components.message_box import MessageBox
from shared.ui.widgets.panel import Panel
from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


class BatchImportDialog(QDialog):
    """Dialog for batch importing documents with options."""

    def __init__(self, parent=None, categories=None):
        """Initialize the batch import dialog.

        Args:
            parent: Parent widget
            categories: List of category objects
        """
        super().__init__(parent)

        self.setWindowTitle("Batch Import Options")
        self.resize(400, 250)
        self.categories = categories or []

        # Initialize UI
        self._init_ui()

    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout()

        # Source selection
        self.import_files_radio = QCheckBox("Import specific files")
        self.import_files_radio.setChecked(True)
        layout.addWidget(self.import_files_radio)

        self.import_dir_radio = QCheckBox("Import from directory")
        layout.addWidget(self.import_dir_radio)

        # Recursive import option
        recursive_layout = QHBoxLayout()
        recursive_layout.addSpacing(20)
        self.recursive_check = QCheckBox("Include subdirectories")
        self.recursive_check.setEnabled(False)
        recursive_layout.addWidget(self.recursive_check)
        layout.addLayout(recursive_layout)

        # Connect checks to enable/disable recursive option
        self.import_dir_radio.toggled.connect(self.recursive_check.setEnabled)

        # Category selection
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("Assign category:"))
        self.category_combo = QComboBox()
        self.category_combo.addItem("None", None)

        # Add available categories
        for category in self.categories:
            self.category_combo.addItem(category.name, category.id)

        category_layout.addWidget(self.category_combo)
        layout.addLayout(category_layout)

        # Thread count
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max parallel imports:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(16)
        self.thread_count.setValue(4)
        thread_layout.addWidget(self.thread_count)
        layout.addLayout(thread_layout)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def get_options(self):
        """Get the selected import options.

        Returns:
            Dictionary with import options
        """
        category_idx = self.category_combo.currentIndex()
        return {
            "import_files": self.import_files_radio.isChecked(),
            "import_dir": self.import_dir_radio.isChecked(),
            "recursive": self.recursive_check.isChecked(),
            "max_workers": self.thread_count.value(),
            "category_id": self.category_combo.itemData(category_idx),
        }


class UnifiedDocumentBrowserPanel(Panel):
    """Unified panel for comprehensive document management."""

    # Signals for document operations
    document_selected = pyqtSignal(str)  # Document ID
    document_opened = pyqtSignal(str)  # Document ID

    def __init__(self, parent=None):
        """Initialize the unified document browser panel.

        Args:
            parent: Parent widget
        """
        super().__init__("", parent)  # Remove title to eliminate white space

        # Create services
        self.document_service = DocumentService()
        self.category_service = CategoryService()

        # State management
        self.state_manager = get_state_manager()
        self.state_manager.register_component("document_browser", self)

        # Track current selection
        self.current_document_id = None
        self.documents = []
        self.categories = {}

        # Initialize UI
        self._init_ui()

        # Load data
        self._load_documents()
        self._load_categories()

        # Connect state manager signals
        self._connect_state_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        # Create main splitter for left (browser) and right (details) panels
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.content_layout.addWidget(main_splitter)

        # Left panel - Document browser
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)  # Reduce spacing between elements

        # Cross-component action bar
        self.action_bar = CrossComponentActionBar("document_browser", self)
        self.action_bar.action_requested.connect(self._handle_cross_component_action)
        self.action_bar.setMaximumHeight(35)  # Limit height
        left_layout.addWidget(self.action_bar)

        # Toolbar with management buttons
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(5)  # Reduce button spacing

        # Categories button (from DocumentManagerPanel)
        self.categories_btn = QPushButton("Categories")
        self.categories_btn.setIcon(QIcon.fromTheme("folder"))
        self.categories_btn.setMaximumHeight(28)
        self.categories_btn.clicked.connect(self._open_category_manager)
        toolbar_layout.addWidget(self.categories_btn)

        # Analyze button (from DocumentManagerPanel)
        self.analyze_btn = QPushButton("Analyze")
        self.analyze_btn.setIcon(QIcon.fromTheme("edit-find"))
        self.analyze_btn.setMaximumHeight(28)
        self.analyze_btn.clicked.connect(self._analyze_document)
        self.analyze_btn.setEnabled(False)  # Disabled until document selected
        toolbar_layout.addWidget(self.analyze_btn)

        # Separator
        toolbar_layout.addWidget(QLabel("|"))

        # Import button
        self.import_btn = QPushButton("Import")
        self.import_btn.setIcon(QIcon.fromTheme("document-open"))
        self.import_btn.setMaximumHeight(28)
        self.import_btn.clicked.connect(self._import_document)
        toolbar_layout.addWidget(self.import_btn)

        # Batch Import button
        self.batch_import_btn = QPushButton("Batch")
        self.batch_import_btn.setIcon(QIcon.fromTheme("folder-open"))
        self.batch_import_btn.setMaximumHeight(28)
        self.batch_import_btn.clicked.connect(self._batch_import_documents)
        toolbar_layout.addWidget(self.batch_import_btn)

        # Export button (from DocumentDatabaseManagerPanel)
        self.export_btn = QPushButton("Export")
        self.export_btn.setIcon(QIcon.fromTheme("document-save"))
        self.export_btn.setMaximumHeight(28)
        self.export_btn.clicked.connect(self._export_document)
        self.export_btn.setEnabled(False)
        toolbar_layout.addWidget(self.export_btn)

        # Delete button (from DocumentDatabaseManagerPanel)
        self.delete_btn = QPushButton("Delete")
        self.delete_btn.setIcon(QIcon.fromTheme("edit-delete"))
        self.delete_btn.setMaximumHeight(28)
        self.delete_btn.clicked.connect(self._delete_document)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)

        # Refresh button
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.setIcon(QIcon.fromTheme("view-refresh"))
        self.refresh_btn.setMaximumHeight(28)
        self.refresh_btn.clicked.connect(self._refresh)
        toolbar_layout.addWidget(self.refresh_btn)

        # Spacer
        toolbar_layout.addStretch()

        # Search input
        toolbar_layout.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search documents...")
        self.search_input.setMaximumHeight(28)
        self.search_input.setMaximumWidth(200)  # Limit width
        self.search_input.textChanged.connect(self._apply_filters)
        toolbar_layout.addWidget(self.search_input)

        # Create toolbar widget with compact height
        toolbar_widget = QWidget()
        toolbar_widget.setLayout(toolbar_layout)
        toolbar_widget.setMaximumHeight(35)  # Limit toolbar height
        left_layout.addWidget(toolbar_widget)

        # Filter bar
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(5)  # Reduce spacing
        filter_layout.setContentsMargins(0, 0, 0, 0)

        # Category filter
        filter_layout.addWidget(QLabel("Category:"))
        self.category_filter = QComboBox()
        self.category_filter.setMaximumHeight(25)  # Compact height
        self.category_filter.currentIndexChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.category_filter)

        # Type filter
        filter_layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.setMaximumHeight(25)  # Compact height
        self.type_filter.addItem("All Types", None)
        for doc_type in DocumentType:
            self.type_filter.addItem(doc_type.name, doc_type)
        self.type_filter.currentIndexChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.type_filter)

        # Create filter widget with compact height
        filter_widget = QWidget()
        filter_widget.setLayout(filter_layout)
        filter_widget.setMaximumHeight(30)  # Limit filter bar height
        left_layout.addWidget(filter_widget)

        # Document tree
        self.document_tree = QTreeWidget()
        self.document_tree.setHeaderLabels(
            ["Name", "Type", "Size", "Modified", "Category"]
        )
        self.document_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.document_tree.customContextMenuRequested.connect(self._show_context_menu)
        self.document_tree.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.document_tree.itemSelectionChanged.connect(self._on_selection_changed)
        self.document_tree.setColumnWidth(0, 300)  # Name column width
        left_layout.addWidget(self.document_tree)

        # Right panel - Document details (from DocumentDatabaseManagerPanel)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Document details header
        details_label = QLabel("Document Details")
        details_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        right_layout.addWidget(details_label)

        # Document metadata
        self.metadata_widget = QWidget()
        metadata_layout = QVBoxLayout(self.metadata_widget)
        metadata_layout.setContentsMargins(0, 0, 0, 0)

        # Document name
        self.doc_name_label = QLabel("No document selected")
        self.doc_name_label.setStyleSheet("font-size: 12pt; font-weight: bold;")
        metadata_layout.addWidget(self.doc_name_label)

        # Document info
        self.doc_info_label = QLabel()
        metadata_layout.addWidget(self.doc_info_label)

        # Document path
        path_layout = QHBoxLayout()
        path_label = QLabel("Path:")
        self.doc_path_label = QLabel()
        self.doc_path_label.setTextInteractionFlags(
            Qt.TextInteractionFlag.TextSelectableByMouse
        )
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.doc_path_label)
        metadata_layout.addLayout(path_layout)

        # Document category
        category_info_layout = QHBoxLayout()
        category_info_label = QLabel("Category:")
        self.doc_category_label = QLabel()
        category_info_layout.addWidget(category_info_label)
        category_info_layout.addWidget(self.doc_category_label)
        metadata_layout.addLayout(category_info_layout)

        right_layout.addWidget(self.metadata_widget)

        # Document content preview
        content_label = QLabel("Document Content")
        content_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        right_layout.addWidget(content_label)

        # Content preview
        self.content_preview = UnicodeTextEdit()
        self.content_preview.setReadOnly(True)
        right_layout.addWidget(self.content_preview)

        # Add widgets to splitter
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(right_widget)
        main_splitter.setSizes([500, 400])  # Left panel larger initially

    def _load_documents(self):
        """Load documents from the service."""
        self.documents = self.document_service.get_all_documents()
        self._update_document_tree()

    def _load_categories(self):
        """Load categories for the filter dropdown."""
        # Clear existing categories
        self.category_filter.clear()

        # Add "All Categories" option
        self.category_filter.addItem("All Categories", None)

        # Add categories from service
        categories = self.category_service.get_all_categories()
        self.categories = {cat.id: cat for cat in categories}
        for category in categories:
            self.category_filter.addItem(category.name, category.id)

    def _update_document_tree(self):
        """Update the document tree with current documents."""
        # Clear current items
        self.document_tree.clear()

        # Apply filters (this will repopulate the tree)
        self._apply_filters()

    def _apply_filters(self):
        """Apply filters to the document list."""
        # Clear current items
        self.document_tree.clear()

        # Get filter values
        search_text = self.search_input.text().lower()
        category_idx = self.category_filter.currentIndex()
        category_id = (
            self.category_filter.itemData(category_idx) if category_idx > 0 else None
        )
        type_idx = self.type_filter.currentIndex()
        doc_type = self.type_filter.itemData(type_idx) if type_idx > 0 else None

        # Get categories map for display
        categories = {cat.id: cat for cat in self.category_service.get_all_categories()}

        # Add filtered documents to tree
        for document in self.documents:
            # Check search filter
            if search_text and search_text not in document.name.lower():
                if not document.content or search_text not in document.content.lower():
                    continue

            # Check category filter
            if category_id and document.category != category_id:
                continue

            # Check type filter
            if doc_type and document.file_type != doc_type:
                continue

            # Create tree item
            item = QTreeWidgetItem(
                [
                    document.name,
                    document.file_type.value.upper(),
                    document.get_file_size_display(),
                    document.last_modified_date.strftime("%Y-%m-%d %H:%M"),
                ]
            )

            # Store document ID in the item
            item.setData(0, Qt.ItemDataRole.UserRole, document.id)

            # Get category name
            if document.category and document.category in categories:
                category_name = categories[document.category].name
            else:
                category_name = ""
            item.setText(4, category_name)

            # Add to tree
            self.document_tree.addTopLevelItem(item)

    def _on_selection_changed(self):
        """Handle selection change in the document tree."""
        selected_items = self.document_tree.selectedItems()
        if not selected_items:
            self.current_document_id = None
            self._clear_document_details()
            self.analyze_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            return

        # Get document ID from the first item
        item = selected_items[0]
        document_id = item.data(0, Qt.ItemDataRole.UserRole)
        self.current_document_id = document_id

        # Enable buttons
        self.analyze_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        self.delete_btn.setEnabled(True)

        # Load document details
        self._load_document_details(document_id)

        # Update state manager
        document = next((doc for doc in self.documents if doc.id == document_id), None)
        self.state_manager.set_current_document(document_id, document, "document_browser")

        # Emit signal
        self.document_selected.emit(document_id)

    def _on_item_double_clicked(self, item, column):
        """Handle double-click on document item.

        Args:
            item: Tree widget item
            column: Column index
        """
        document_id = item.data(0, Qt.ItemDataRole.UserRole)
        if document_id:
            self._open_document(document_id)

    def _clear_document_details(self):
        """Clear the document details panel."""
        self.doc_name_label.setText("No document selected")
        self.doc_info_label.setText("")
        self.doc_path_label.setText("")
        self.doc_category_label.setText("")
        self.content_preview.setText("Select a document to view its details.")

    def _load_document_details(self, document_id: str):
        """Load document details into the right panel.

        Args:
            document_id: Document ID
        """
        document = self.document_service.get_document(document_id)
        if not document:
            self._clear_document_details()
            return

        # Update document details
        self.doc_name_label.setText(document.name)

        # Document info
        info_text = f"Type: {document.file_type.value.upper()} | Size: {self._format_size(document.size_bytes)}"
        if document.word_count:
            info_text += f" | Words: {document.word_count}"
        if document.page_count:
            info_text += f" | Pages: {document.page_count}"
        self.doc_info_label.setText(info_text)

        # Document path
        self.doc_path_label.setText(str(document.file_path))

        # Document category
        category_name = "None"
        if document.category and document.category in self.categories:
            category_name = self.categories[document.category].name
        self.doc_category_label.setText(category_name)

        # Document content preview
        if document.content:
            DocumentPreviewRenderer.set_preview_content(self.content_preview, document.content)
        else:
            self.content_preview.setText("No content available for this document.")

    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format.

        Args:
            size_bytes: Size in bytes

        Returns:
            Formatted size string
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"



    def _refresh(self):
        """Refresh the document list."""
        self._load_documents()
        self._load_categories()

    def _open_category_manager(self):
        """Open the category manager dialog."""
        dialog = CategoryManagerDialog(self)
        if dialog.exec():
            # Refresh document browser if categories changed
            self._refresh()

    def _open_document(self, document_id):
        """Open a document for viewing.

        Args:
            document_id: Document ID
        """
        dialog = DocumentViewerDialog(document_id, self)
        dialog.exec()
        self.document_opened.emit(document_id)

    def _analyze_document(self):
        """Open the unified analysis dialog for the selected document."""
        if not self.current_document_id:
            return

        # Get the document
        document = self.document_service.get_document(self.current_document_id)
        if not document:
            MessageBox.warning(self, "Document Not Found", "Selected document could not be loaded.")
            return

        # Get document text content
        text_content = ""
        if document.extracted_text:
            text_content = document.extracted_text
        elif document.content:
            text_content = document.content
        elif document.file_path:
            # Try to extract text if not already done
            try:
                self.document_service.extract_text(document)
                text_content = document.extracted_text or ""
            except Exception as e:
                logger.warning(f"Could not extract text from document {document.id}: {e}")

        if not text_content:
            MessageBox.warning(
                self, "No Text Content",
                f"No text content available for document '{document.name}'.\n"
                "The document may be empty or in an unsupported format."
            )
            return

        # Create and show the unified analysis dialog
        from document_manager.ui.dialogs.unified_analysis_dialog import UnifiedAnalysisDialog

        dialog = UnifiedAnalysisDialog(self, initial_text=text_content, initial_title=document.name)

        # Connect signals if needed
        dialog.analysis_completed.connect(self._on_analysis_completed)

        # Show dialog
        dialog.exec()

    def _on_analysis_completed(self, results: dict):
        """Handle completion of document analysis."""
        analysis_type = results.get('type', 'analysis')

        # Show completion message
        if analysis_type == 'gematria':
            result = results.get('result')
            if result:
                MessageBox.information(
                    self, "Analysis Complete",
                    f"Gematria analysis completed!\n"
                    f"Analyzed {result.unique_words} unique words."
                )
        elif analysis_type == 'verse_breakdown':
            breakdown = results.get('breakdown')
            if breakdown:
                MessageBox.information(
                    self, "Analysis Complete",
                    f"Verse {breakdown.verse_number} breakdown completed!\n"
                    f"Analyzed {len(breakdown.words)} words."
                )
        elif analysis_type == 'concordance':
            MessageBox.information(
                self, "Analysis Complete",
                "Concordance creation completed successfully!"
            )

    def _import_document(self):
        """Import a document using the unified import dialog."""
        from document_manager.ui.dialogs.unified_import_dialog import UnifiedImportDialog

        dialog = UnifiedImportDialog(self)

        # Connect signals to handle import completion
        dialog.documents_imported.connect(self._on_documents_imported)

        # Show dialog
        dialog.exec()

    def _on_documents_imported(self, imported_files: list):
        """Handle successful document import."""
        if imported_files:
            MessageBox.information(
                self,
                "Import Complete",
                f"Successfully imported {len(imported_files)} document(s).",
            )

            # Refresh document list
            self._refresh()

    def _batch_import_documents(self):
        """Import documents in batch mode using the unified import dialog."""
        from document_manager.ui.dialogs.unified_import_dialog import UnifiedImportDialog

        dialog = UnifiedImportDialog(self)

        # Connect signals to handle import completion
        dialog.documents_imported.connect(self._on_documents_imported)

        # Show dialog and switch to the standard import tab (which supports batch operations)
        dialog.exec()

    def _export_document(self):
        """Export the selected document."""
        if not self.current_document_id:
            return

        document = self.document_service.get_document(self.current_document_id)
        if not document:
            MessageBox.error(self, "Error", "Document not found.")
            return

        # Open file dialog for export location
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.FileMode.AnyFile)
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)

        # Set default filename and extension
        default_name = f"{document.name}.{document.file_type.value}"
        file_path, _ = file_dialog.getSaveFileName(
            self, "Export Document", default_name, f"*{document.file_type.value}"
        )

        if file_path:
            try:
                # Copy the document file to the export location
                import shutil
                shutil.copy2(document.file_path, file_path)

                MessageBox.information(
                    self, "Export Complete", f"Document exported to: {file_path}"
                )
            except Exception as e:
                MessageBox.error(
                    self, "Export Error", f"Failed to export document: {str(e)}"
                )

    def _delete_document(self):
        """Delete the selected document."""
        if not self.current_document_id:
            return

        document = self.document_service.get_document(self.current_document_id)
        if not document:
            MessageBox.error(self, "Error", "Document not found.")
            return

        # Confirm deletion
        from PyQt6.QtWidgets import QMessageBox
        result = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete '{document.name}'?\n\n"
            "This will permanently remove the document from the database and delete the file.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if result == QMessageBox.StandardButton.Yes:
            try:
                # Delete the document
                self.document_service.delete_document(self.current_document_id)

                MessageBox.information(
                    self, "Document Deleted", f"'{document.name}' has been deleted."
                )

                # Clear selection and refresh
                self.current_document_id = None
                self._clear_document_details()
                self._refresh()

            except Exception as e:
                MessageBox.error(
                    self, "Delete Error", f"Failed to delete document: {str(e)}"
                )

    def _show_context_menu(self, position):
        """Show context menu for document operations.

        Args:
            position: Position where menu should appear
        """
        item = self.document_tree.itemAt(position)
        if not item:
            return

        # Get document ID
        document_id = item.data(0, Qt.ItemDataRole.UserRole)
        if not document_id:
            return

        # Get document for tag information
        document = self.document_service.get_document(document_id)
        if not document:
            return

        # Create context menu
        menu = QMenu()

        # Add actions
        open_action = QAction("Open", self)
        open_action.triggered.connect(lambda: self._open_document(document_id))
        menu.addAction(open_action)

        # Add Edit in RTF Editor action
        edit_rtf_action = QAction("Edit in RTF Editor", self)
        edit_rtf_action.triggered.connect(
            lambda: self._edit_document_in_rtf(document_id)
        )
        menu.addAction(edit_rtf_action)

        view_action = QAction("View Details", self)
        view_action.triggered.connect(lambda: self._view_document(document_id))
        menu.addAction(view_action)

        menu.addSeparator()

        # Export action
        export_action = QAction("Export", self)
        export_action.triggered.connect(lambda: self._export_document_by_id(document_id))
        menu.addAction(export_action)

        # Delete action
        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(lambda: self._delete_document_by_id(document_id))
        menu.addAction(delete_action)

        menu.addSeparator()

        # Category submenu
        category_menu = QMenu("Set Category", self)

        # Add "None" option
        none_action = QAction("None", self)
        none_action.triggered.connect(
            lambda: self._set_document_category(document_id, None)
        )
        category_menu.addAction(none_action)

        # Add categories
        for category in self.category_service.get_all_categories():
            cat_action = QAction(category.name, self)
            cat_action.triggered.connect(
                lambda checked, cat_id=category.id: self._set_document_category(
                    document_id, cat_id
                )
            )
            category_menu.addAction(cat_action)

        menu.addMenu(category_menu)

        # Show menu
        menu.exec(self.document_tree.mapToGlobal(position))

    def _export_document_by_id(self, document_id: str):
        """Export a document by ID.

        Args:
            document_id: Document ID
        """
        # Set current document and export
        old_id = self.current_document_id
        self.current_document_id = document_id
        self._export_document()
        self.current_document_id = old_id

    def _delete_document_by_id(self, document_id: str):
        """Delete a document by ID.

        Args:
            document_id: Document ID
        """
        # Set current document and delete
        old_id = self.current_document_id
        self.current_document_id = document_id
        self._delete_document()
        self.current_document_id = old_id

    def _edit_document_in_rtf(self, document_id: str):
        """Edit a document in the RTF editor.

        Args:
            document_id: Document ID
        """
        # This would open the RTF editor with the document
        # For now, just show a message
        MessageBox.information(
            self, "RTF Editor", "RTF Editor integration will be implemented."
        )

    def _view_document(self, document_id: str):
        """View document details.

        Args:
            document_id: Document ID
        """
        dialog = DocumentViewerDialog(document_id, self)
        dialog.exec()

    def _set_document_category(self, document_id: str, category_id: Optional[str]):
        """Set the category for a document.

        Args:
            document_id: Document ID
            category_id: Category ID or None
        """
        try:
            # Update document category
            document = self.document_service.get_document(document_id)
            if document:
                document.category = category_id
                self.document_service.update_document(document)

                # Refresh the display
                self._refresh()

                # Show success message
                category_name = "None"
                if category_id and category_id in self.categories:
                    category_name = self.categories[category_id].name

                MessageBox.information(
                    self,
                    "Category Updated",
                    f"Document category set to: {category_name}"
                )
        except Exception as e:
            MessageBox.error(
                self, "Error", f"Failed to update document category: {str(e)}"
            )

    def _connect_state_signals(self):
        """Connect to state manager signals for cross-component coordination."""
        self.state_manager.navigation_requested.connect(self._handle_navigation_request)
        self.state_manager.search_performed.connect(self._handle_search_from_other_component)

    def _handle_cross_component_action(self, target_component: str, action: str, params: dict):
        """Handle cross-component action requests.

        Args:
            target_component: Target component name
            action: Action to perform
            params: Action parameters
        """
        from loguru import logger
        logger.debug(f"Cross-component action: {target_component}.{action}")

        if target_component == "advanced_search" and action == "search_document":
            # Open Advanced Search with current document
            self._open_advanced_search_window()
        elif target_component == "knowledge_base" and action == "add_document":
            # Open Knowledge Base with current document
            self._open_knowledge_base_window()

    def _handle_navigation_request(self, target_component: str, action: str, params: dict):
        """Handle navigation requests from other components.

        Args:
            target_component: Target component name
            action: Action to perform
            params: Action parameters
        """
        if target_component == "document_browser":
            if action == "show_document" and 'document_id' in params:
                self._select_document_by_id(params['document_id'])
            elif action == "search" and 'search_query' in params:
                self.search_input.setText(params['search_query'])
                self._apply_filters()

    def _handle_search_from_other_component(self, search_context):
        """Handle search performed in other components.

        Args:
            search_context: Search context from state manager
        """
        if search_context.component_source != "document_browser":
            # Update our search field to match
            self.search_input.setText(search_context.query)
            self._apply_filters()

    def _select_document_by_id(self, document_id: str):
        """Select a document by its ID.

        Args:
            document_id: Document ID to select
        """
        # Find the item in the tree
        for i in range(self.document_tree.topLevelItemCount()):
            item = self.document_tree.topLevelItem(i)
            if item.data(0, Qt.ItemDataRole.UserRole) == document_id:
                self.document_tree.setCurrentItem(item)
                self.document_tree.scrollToItem(item)
                break

    def _open_advanced_search_window(self):
        """Open the Advanced Document Search window."""
        try:
            from document_manager.ui.panels.advanced_document_search_panel import (
                AdvancedDocumentSearchPanel,
            )

            # Find the window manager
            window_manager = self._get_window_manager()
            if not window_manager:
                logger.error("Cannot find window manager to open Advanced Search window")
                return

            # Create the panel
            panel = AdvancedDocumentSearchPanel()

            # Create and open the advanced search window
            window_manager.open_window("advanced_document_search", panel)

            # Set the window title
            panel.setWindowTitle("Advanced Document Search")

            logger.debug("Opened Advanced Document Search window from Document Browser")
        except Exception as e:
            logger.error(f"Failed to open Advanced Search window: {e}")

    def _open_knowledge_base_window(self):
        """Open the Knowledge Base window."""
        try:
            # Use the simplified Knowledge Base panel that integrates with existing infrastructure
            from document_manager.ui.panels.simple_knowledge_base_panel import SimpleKnowledgeBasePanel

            # Find the window manager
            window_manager = self._get_window_manager()
            if not window_manager:
                logger.error("Cannot find window manager to open Knowledge Base window")
                return

            # Create the panel
            panel = SimpleKnowledgeBasePanel()

            # Create and open the Knowledge Base window
            window_manager.open_window("knowledge_base", panel)

            # Set the window title
            panel.setWindowTitle("Knowledge Base")

            logger.debug("Opened Knowledge Base window from Document Browser")
        except Exception as e:
            logger.error(f"Failed to open Knowledge Base window: {e}")

    def _get_window_manager(self):
        """Get the window manager from the parent widget chain.

        Returns:
            WindowManager instance or None if not found
        """
        from shared.ui.window_management import WindowManager
        from shared.utils.app import MainWindow

        result = None

        try:
            # Start with the current parent
            parent = self.parent()

            # Check parent chain
            while parent is not None and result is None:
                # Check if parent has window_manager attribute
                if hasattr(parent, "window_manager"):
                    potential_manager = getattr(parent, "window_manager")
                    if isinstance(potential_manager, WindowManager):
                        result = potential_manager

                # Move up to parent if we haven't found a manager
                if result is None:
                    parent = parent.parent()

            # If we still haven't found it, try to get it from the main window
            if result is None:
                # Try to find all MainWindow instances
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    for widget in app.allWidgets():
                        if isinstance(widget, MainWindow) and hasattr(widget, "window_manager"):
                            result = widget.window_manager
                            logger.debug("Found window manager from MainWindow instance")
                            break

            # Log if we didn't find anything
            if result is None:
                logger.error("No window manager found anywhere - this should not happen")

        except Exception as e:
            logger.error(f"Error while finding window manager: {e}")

        return result
