"""
Cross-Component Action Widgets

Provides reusable UI components for cross-component navigation and actions
in the document management system.
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QAction
from PyQt6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QPushButton, QMenu, QToolButton,
    QLabel, QFrame, QButtonGroup, QToolBar, QActionGroup
)

from loguru import logger
from document_manager.services.document_state_manager import get_state_manager


class CrossComponentActionBar(QWidget):
    """Action bar for cross-component navigation and operations."""
    
    # Signals
    action_requested = pyqtSignal(str, str, dict)  # component, action, params
    
    def __init__(self, current_component: str, parent=None):
        """Initialize the action bar.
        
        Args:
            current_component: Name of the current component
            parent: Parent widget
        """
        super().__init__(parent)
        self.current_component = current_component
        self.state_manager = get_state_manager()
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # Component navigation buttons
        self._create_navigation_buttons(layout)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # Quick action buttons
        self._create_quick_actions(layout)
        
        # Stretch to push everything to the left
        layout.addStretch()
        
        # Context info
        self.context_label = QLabel("Ready")
        self.context_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.context_label)
    
    def _create_navigation_buttons(self, layout):
        """Create navigation buttons for other components."""
        # Document Browser
        if self.current_component != "document_browser":
            browser_btn = QPushButton("📁 Browse")
            browser_btn.setToolTip("Open in Document Browser")
            browser_btn.clicked.connect(lambda: self._navigate_to("document_browser", "show_document"))
            layout.addWidget(browser_btn)
        
        # Advanced Search
        if self.current_component != "advanced_search":
            search_btn = QPushButton("🔍 Search")
            search_btn.setToolTip("Open in Advanced Search")
            search_btn.clicked.connect(lambda: self._navigate_to("advanced_search", "search_document"))
            layout.addWidget(search_btn)
        
        # Knowledge Base
        if self.current_component != "knowledge_base":
            kb_btn = QPushButton("📚 Knowledge")
            kb_btn.setToolTip("Open in Knowledge Base")
            kb_btn.clicked.connect(lambda: self._navigate_to("knowledge_base", "add_document"))
            layout.addWidget(kb_btn)
    
    def _create_quick_actions(self, layout):
        """Create quick action buttons."""
        # Quick actions menu
        actions_btn = QToolButton()
        actions_btn.setText("⚡ Actions")
        actions_btn.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        actions_btn.setToolTip("Quick actions for selected documents")
        
        # Create actions menu
        actions_menu = QMenu(actions_btn)
        
        # Add to Knowledge Base
        add_kb_action = QAction("📚 Add to Knowledge Base", self)
        add_kb_action.triggered.connect(lambda: self._quick_action("add_to_knowledge_base"))
        actions_menu.addAction(add_kb_action)
        
        # Export selection
        export_action = QAction("📤 Export Selection", self)
        export_action.triggered.connect(lambda: self._quick_action("export_selection"))
        actions_menu.addAction(export_action)
        
        # Bulk categorize
        categorize_action = QAction("🏷️ Bulk Categorize", self)
        categorize_action.triggered.connect(lambda: self._quick_action("bulk_categorize"))
        actions_menu.addAction(categorize_action)
        
        actions_menu.addSeparator()
        
        # Advanced search on selection
        search_selection_action = QAction("🔍 Search in Selection", self)
        search_selection_action.triggered.connect(lambda: self._quick_action("search_in_selection"))
        actions_menu.addAction(search_selection_action)
        
        actions_btn.setMenu(actions_menu)
        layout.addWidget(actions_btn)
    
    def _connect_signals(self):
        """Connect to state manager signals."""
        self.state_manager.document_selected.connect(self._on_document_selected)
        self.state_manager.search_performed.connect(self._on_search_performed)
        self.state_manager.state_changed.connect(self._on_state_changed)
    
    def _navigate_to(self, target_component: str, action: str):
        """Navigate to another component."""
        context = self.state_manager.get_current_context()
        params = {
            'source_component': self.current_component,
            'context': context
        }
        
        self.action_requested.emit(target_component, action, params)
        self.state_manager.navigate_to_component(target_component, action, params)
        
        logger.debug(f"Navigation requested: {target_component}.{action}")
    
    def _quick_action(self, action: str):
        """Perform a quick action."""
        context = self.state_manager.get_current_context()
        params = {
            'action': action,
            'source_component': self.current_component,
            'context': context
        }
        
        self.action_requested.emit(self.current_component, action, params)
        
        logger.debug(f"Quick action: {action}")
    
    def _on_document_selected(self, document_id: str, source_component: str):
        """Handle document selection."""
        if source_component != self.current_component:
            self.context_label.setText(f"Document: {document_id[:8]}...")
    
    def _on_search_performed(self, search_context):
        """Handle search performed."""
        if search_context.component_source != self.current_component:
            query = search_context.query[:20] + "..." if len(search_context.query) > 20 else search_context.query
            self.context_label.setText(f"Search: {query}")
    
    def _on_state_changed(self, component_name: str, state_data: Dict[str, Any]):
        """Handle state changes."""
        if component_name != self.current_component:
            # Update context display based on state changes
            pass


class DocumentContextWidget(QWidget):
    """Widget showing current document context and quick navigation."""
    
    def __init__(self, parent=None):
        """Initialize the context widget."""
        super().__init__(parent)
        self.state_manager = get_state_manager()
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Title
        title_label = QLabel("Document Context")
        title_label.setStyleSheet("font-weight: bold; color: #333;")
        layout.addWidget(title_label)
        
        # Current document
        self.current_doc_label = QLabel("No document selected")
        self.current_doc_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.current_doc_label)
        
        # Search context
        self.search_label = QLabel("No recent search")
        self.search_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.search_label)
        
        # Quick navigation
        nav_layout = QHBoxLayout()
        
        self.browser_btn = QPushButton("Browser")
        self.browser_btn.setMaximumHeight(25)
        self.browser_btn.clicked.connect(lambda: self._navigate("document_browser"))
        nav_layout.addWidget(self.browser_btn)
        
        self.search_btn = QPushButton("Search")
        self.search_btn.setMaximumHeight(25)
        self.search_btn.clicked.connect(lambda: self._navigate("advanced_search"))
        nav_layout.addWidget(self.search_btn)
        
        self.kb_btn = QPushButton("KB")
        self.kb_btn.setMaximumHeight(25)
        self.kb_btn.clicked.connect(lambda: self._navigate("knowledge_base"))
        nav_layout.addWidget(self.kb_btn)
        
        layout.addLayout(nav_layout)
    
    def _connect_signals(self):
        """Connect to state manager signals."""
        self.state_manager.document_selected.connect(self._update_document_context)
        self.state_manager.search_performed.connect(self._update_search_context)
    
    def _update_document_context(self, document_id: str, source_component: str):
        """Update document context display."""
        doc_display = document_id[:12] + "..." if len(document_id) > 12 else document_id
        self.current_doc_label.setText(f"📄 {doc_display}")
    
    def _update_search_context(self, search_context):
        """Update search context display."""
        query_display = search_context.query[:15] + "..." if len(search_context.query) > 15 else search_context.query
        self.search_label.setText(f"🔍 {query_display}")
    
    def _navigate(self, target_component: str):
        """Navigate to a component."""
        self.state_manager.navigate_to_component(target_component, "show", {})


class QuickActionToolbar(QToolBar):
    """Toolbar with quick actions for document operations."""
    
    def __init__(self, component_name: str, parent=None):
        """Initialize the toolbar.
        
        Args:
            component_name: Name of the component using this toolbar
            parent: Parent widget
        """
        super().__init__(parent)
        self.component_name = component_name
        self.state_manager = get_state_manager()
        
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.setMovable(False)
        
        self._create_actions()
    
    def _create_actions(self):
        """Create toolbar actions."""
        # Browse action
        browse_action = QAction("📁", self)
        browse_action.setText("Browse")
        browse_action.setToolTip("Open in Document Browser")
        browse_action.triggered.connect(lambda: self._navigate("document_browser", "show"))
        self.addAction(browse_action)
        
        # Search action
        search_action = QAction("🔍", self)
        search_action.setText("Search")
        search_action.setToolTip("Open in Advanced Search")
        search_action.triggered.connect(lambda: self._navigate("advanced_search", "search"))
        self.addAction(search_action)
        
        # Knowledge Base action
        kb_action = QAction("📚", self)
        kb_action.setText("Knowledge")
        kb_action.setToolTip("Open in Knowledge Base")
        kb_action.triggered.connect(lambda: self._navigate("knowledge_base", "add"))
        self.addAction(kb_action)
        
        self.addSeparator()
        
        # Export action
        export_action = QAction("📤", self)
        export_action.setText("Export")
        export_action.setToolTip("Export current selection")
        export_action.triggered.connect(self._export_selection)
        self.addAction(export_action)
    
    def _navigate(self, target_component: str, action: str):
        """Navigate to another component."""
        self.state_manager.navigate_to_component(target_component, action, {
            'source': self.component_name
        })
    
    def _export_selection(self):
        """Export current selection."""
        # This would be implemented based on the specific component's needs
        logger.debug(f"Export requested from {self.component_name}")
