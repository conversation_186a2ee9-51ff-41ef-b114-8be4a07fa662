"""
Purpose: Provides shared utilities for document content preview functionality.

This file is part of the document_manager pillar and serves as a utility module.
It consolidates document preview logic that was previously duplicated across multiple components.

Key components:
- DocumentPreviewRenderer: Utility class for smart document content rendering

Dependencies:
- PyQt6: For UI components
- loguru: For logging
"""

from loguru import logger
from PyQt6.QtWidgets import QTextEdit

from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


class DocumentPreviewRenderer:
    """Utility class for smart document content preview rendering.
    
    This class consolidates the logic for detecting HTML content, handling images,
    and applying appropriate size limits for document previews.
    """
    
    @staticmethod
    def set_preview_content(text_widget: UnicodeTextEdit, content: str, max_chars_override: int = None):
        """Set preview content with smart HTML detection and rendering.

        Args:
            text_widget: The text widget to set content on
            content: Document content to preview
            max_chars_override: Optional override for maximum character limit
        """
        if not content:
            text_widget.setText("No content available.")
            return

        # Check if content contains HTML tags (especially img tags for images)
        has_html_tags = '<' in content and '>' in content
        has_images = '<img ' in content.lower()
        has_data_uri = 'data:image/' in content.lower()

        # Determine size limits based on content type
        if max_chars_override:
            max_chars = max_chars_override
        elif has_images or has_data_uri:
            # For content with images, be much more generous with size limits
            # Base64 images can be very large, so we need to accommodate them
            max_chars = 5000000  # 5MB for content with images
        else:
            max_chars = 100000   # 100KB for regular content

        preview_content = content
        content_truncated = False

        # Handle content truncation if needed
        if len(content) > max_chars:
            if has_images and has_html_tags:
                # For HTML with images, try to preserve complete image tags
                truncate_pos = max_chars
                # Look for the last complete image tag before the limit
                last_img_end = content.rfind('>', 0, max_chars)
                if last_img_end > max_chars * 0.8:  # If we found a tag end reasonably close
                    truncate_pos = last_img_end + 1

                preview_content = content[:truncate_pos]
                content_truncated = True
            else:
                preview_content = content[:max_chars]
                content_truncated = True

        # Add truncation notice if content was truncated
        if content_truncated:
            preview_content += "\n\n[Content truncated due to size]"

        # Set content based on type
        if has_html_tags:
            logger.debug(f"Rendering content as HTML (has images: {has_images}, has data URIs: {has_data_uri})")
            text_widget.setHtml(preview_content)
        else:
            logger.debug("Rendering content as plain text")
            text_widget.setText(preview_content)

    @staticmethod
    def set_preview_content_for_search(text_widget: QTextEdit, content: str):
        """Set preview content optimized for search functionality.
        
        This variant is optimized for search panels where we want to be more generous
        with HTML rendering to preserve formatting for search highlighting.

        Args:
            text_widget: The text widget to set content on (can be QTextEdit or UnicodeTextEdit)
            content: Document content to preview
        """
        if not content:
            if hasattr(text_widget, 'setPlainText'):
                text_widget.setPlainText("No content available.")
            else:
                text_widget.setText("No content available.")
            return

        # Check if content contains HTML tags (especially img tags for images)
        has_html_tags = '<' in content and '>' in content
        has_images = '<img ' in content.lower()
        has_data_uri = 'data:image/' in content.lower()

        # Decide how to render the content - be generous with image content
        if has_html_tags and (has_images or has_data_uri):
            # Content has HTML with images - render as HTML (no size limit for images)
            logger.debug(f"Search preview: Rendering content as HTML (has images: {has_images}, has data URIs: {has_data_uri})")
            # Improve HTML paragraph formatting
            formatted_html = DocumentPreviewRenderer._format_html_paragraphs(content)
            text_widget.setHtml(formatted_html)
        elif has_html_tags and len(content) < 5000000:  # Render HTML content up to 5MB
            # Content has HTML but no images - still render as HTML for formatting
            logger.debug("Search preview: Rendering content as HTML (has HTML tags)")
            # Improve HTML paragraph formatting
            formatted_html = DocumentPreviewRenderer._format_html_paragraphs(content)
            text_widget.setHtml(formatted_html)
        else:
            # Content is plain text or very large HTML - render as plain text
            logger.debug("Search preview: Rendering content as plain text")
            # Improve paragraph formatting for plain text
            formatted_content = DocumentPreviewRenderer._format_plain_text_paragraphs(content)
            if hasattr(text_widget, 'setPlainText'):
                text_widget.setPlainText(formatted_content)
            else:
                text_widget.setText(formatted_content)

    @staticmethod
    def set_preview_content_for_viewer(text_widget: UnicodeTextEdit, content: str):
        """Set preview content optimized for document viewer dialogs.
        
        This variant is optimized for document viewers where we want maximum
        compatibility and can handle larger content sizes.

        Args:
            text_widget: The text widget to set content on
            content: Document content to preview
        """
        if not content:
            text_widget.setText("No content available.")
            return

        # Check if content contains HTML tags (especially img tags for images)
        has_html_tags = '<' in content and '>' in content
        has_images = '<img ' in content.lower()
        has_data_uri = 'data:image/' in content.lower()

        # Decide how to render the content - be generous with image content
        if has_html_tags and (has_images or has_data_uri):
            # Content has HTML with images - render as HTML (no size limit for images)
            logger.debug(f"Document viewer: Rendering content as HTML (has images: {has_images}, has data URIs: {has_data_uri})")
            # Improve HTML paragraph formatting
            formatted_html = DocumentPreviewRenderer._format_html_paragraphs(content)
            text_widget.setHtml(formatted_html)
        elif has_html_tags and len(content) < 10000000:  # Render HTML content up to 10MB
            # Content has HTML but no images - still render as HTML for formatting
            logger.debug("Document viewer: Rendering content as HTML (has HTML tags)")
            # Improve HTML paragraph formatting
            formatted_html = DocumentPreviewRenderer._format_html_paragraphs(content)
            text_widget.setHtml(formatted_html)
        else:
            # Content is plain text or extremely large HTML - render as plain text
            logger.debug("Document viewer: Rendering content as plain text")
            # Improve paragraph formatting for plain text
            formatted_content = DocumentPreviewRenderer._format_plain_text_paragraphs(content)
            text_widget.setText(formatted_content)

    @staticmethod
    def _format_plain_text_paragraphs(content: str) -> str:
        """Format plain text content to improve paragraph separation.

        Args:
            content: Raw text content

        Returns:
            Formatted text with better paragraph separation
        """
        if not content:
            return content

        # Split into lines and process
        lines = content.split('\n')
        formatted_lines = []

        for i, line in enumerate(lines):
            stripped_line = line.strip()

            # Keep the line as is
            formatted_lines.append(line)

            # Add extra spacing after paragraphs (lines that end sentences)
            if stripped_line and (
                stripped_line.endswith('.') or
                stripped_line.endswith('!') or
                stripped_line.endswith('?') or
                stripped_line.endswith(':')
            ):
                # Check if next line starts a new paragraph (not empty and not indented continuation)
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line and not next_line[0].islower():
                        # Add extra line break for paragraph separation
                        formatted_lines.append('')

        return '\n'.join(formatted_lines)

    @staticmethod
    def _format_html_paragraphs(content: str) -> str:
        """Format HTML content to improve paragraph separation.

        Args:
            content: HTML content

        Returns:
            HTML with improved paragraph formatting
        """
        if not content:
            return content

        import re

        # First, let's add some basic CSS for better paragraph spacing
        css_style = """
        <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 10px;
        }
        p {
            margin-bottom: 1.2em;
            margin-top: 0;
        }
        br + br {
            display: block;
            margin: 0.8em 0;
            content: "";
        }
        </style>
        """

        # If content doesn't start with <html>, wrap it
        if not content.strip().lower().startswith('<html'):
            # Look for existing head tag
            if '<head>' in content.lower():
                # Insert CSS into existing head
                content = re.sub(r'(<head[^>]*>)', r'\1' + css_style, content, flags=re.IGNORECASE)
            else:
                # Add head with CSS at the beginning
                content = css_style + content

        # Convert multiple <br> tags to paragraph breaks
        # Replace <br><br> or <br/><br/> with paragraph breaks
        content = re.sub(r'<br\s*/?>\s*<br\s*/?>', '</p><p>', content, flags=re.IGNORECASE)

        # Convert sequences of line breaks to paragraphs
        # Split content by double line breaks and wrap in <p> tags
        if '<p>' not in content.lower():
            # Split by double newlines and create paragraphs
            parts = re.split(r'\n\s*\n', content)
            formatted_parts = []

            for part in parts:
                part = part.strip()
                if part:
                    # Don't wrap if it's already HTML tags or contains images
                    if not (part.startswith('<') and part.endswith('>')) and '<img' not in part.lower():
                        # Replace single line breaks with spaces within paragraphs
                        part = re.sub(r'\n', ' ', part)
                        part = f'<p>{part}</p>'
                    formatted_parts.append(part)

            content = '\n'.join(formatted_parts)

        return content
