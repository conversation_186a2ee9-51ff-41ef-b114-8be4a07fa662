#!/usr/bin/env python3
"""
Test script to verify that <br> tags are properly converted to line breaks.

This script tests the document conversion service to ensure that line breaks
in the original content are properly converted to HTML <br> tags and then
correctly displayed as line breaks in the RTF editor.
"""

import sys
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from document_manager.services.document_conversion_service import DocumentConversionService


def test_plain_text_to_html_conversion():
    """Test that plain text with line breaks converts correctly to HTML."""
    print("=== Testing Plain Text to HTML Conversion ===")
    
    service = DocumentConversionService()
    
    # Test content with line breaks
    test_content = """This is the first line.
This is the second line.

This is after a blank line.
And this is another line."""
    
    print("Original content:")
    print(repr(test_content))
    print("\nOriginal content (formatted):")
    print(test_content)
    
    # Convert to HTML
    html_result = service._plain_text_to_html(test_content)
    
    print("\nGenerated HTML:")
    print(html_result)
    
    # Check if <br> tags are present and not escaped
    if '<br>' in html_result:
        print("✅ Found <br> tags in HTML")
    else:
        print("❌ No <br> tags found in HTML")
    
    if '&lt;br&gt;' in html_result:
        print("❌ Found escaped <br> tags (&lt;br&gt;) - this is the bug!")
    else:
        print("✅ No escaped <br> tags found")
    
    # Count line breaks
    br_count = html_result.count('<br>')
    escaped_br_count = html_result.count('&lt;br&gt;')
    
    print(f"\nStatistics:")
    print(f"- Proper <br> tags: {br_count}")
    print(f"- Escaped &lt;br&gt; tags: {escaped_br_count}")
    
    return html_result


def test_docx_content_conversion():
    """Test DOCX-style content conversion."""
    print("\n=== Testing DOCX Content Conversion ===")
    
    service = DocumentConversionService()
    
    # Test content that might come from DOCX extraction
    docx_content = """Heading 1

This is a paragraph with
line breaks in the middle.

Another paragraph here.

[TABLE]
Column 1 | Column 2 | Column 3
Data 1 | Data 2 | Data 3
[/TABLE]

Final paragraph."""
    
    print("DOCX-style content:")
    print(docx_content)
    
    # Convert to HTML
    html_result = service._convert_docx_to_html(docx_content)
    
    print("\nGenerated HTML:")
    print(html_result)
    
    # Check for proper HTML structure
    if '<br>' in html_result and '&lt;br&gt;' not in html_result:
        print("✅ DOCX conversion looks good")
    else:
        print("❌ DOCX conversion has issues")
    
    return html_result


def test_html_to_plain_text_conversion():
    """Test that HTML with <br> tags converts back to plain text correctly."""
    print("\n=== Testing HTML to Plain Text Conversion ===")
    
    # Import the HTML to plain text function
    from document_manager.ui.document_tab import DocumentTab
    
    # Create a dummy DocumentTab instance to access the method
    class MockDocumentTab(DocumentTab):
        def __init__(self):
            # Skip the full initialization
            pass
    
    mock_tab = MockDocumentTab()
    
    # Test HTML with <br> tags
    test_html = """<html><body>
<p>This is the first line.<br>This is the second line.</p>
<br>
<p>This is after a blank line.<br>And this is another line.</p>
</body></html>"""
    
    print("Test HTML:")
    print(test_html)
    
    # Convert back to plain text
    plain_result = mock_tab._html_to_plain_text(test_html)
    
    print("\nConverted back to plain text:")
    print(repr(plain_result))
    print("\nFormatted plain text:")
    print(plain_result)
    
    # Check if line breaks are preserved
    line_count = plain_result.count('\n')
    print(f"\nLine breaks in result: {line_count}")
    
    return plain_result


def test_full_conversion_cycle():
    """Test the full conversion cycle: text -> HTML -> RTF Editor -> plain text."""
    print("\n=== Testing Full Conversion Cycle ===")
    
    service = DocumentConversionService()
    
    # Original text with line breaks
    original_text = """Line 1
Line 2

Line 4 (after blank line)
Line 5"""
    
    print("Original text:")
    print(repr(original_text))
    
    # Step 1: Convert to HTML
    html_content = service._plain_text_to_html(original_text)
    print(f"\nStep 1 - HTML conversion:")
    print(f"Contains <br>: {'<br>' in html_content}")
    print(f"Contains escaped <br>: {'&lt;br&gt;' in html_content}")
    
    # Step 2: Simulate RTF Editor processing
    # (This would normally happen when loading into the RTF editor)
    print(f"\nStep 2 - RTF Editor would process this HTML")
    
    # Step 3: Convert back to plain text (simulate export)
    from document_manager.ui.document_tab import DocumentTab
    
    class MockDocumentTab(DocumentTab):
        def __init__(self):
            pass
    
    mock_tab = MockDocumentTab()
    final_text = mock_tab._html_to_plain_text(html_content)
    
    print(f"\nStep 3 - Back to plain text:")
    print(repr(final_text))
    
    # Compare original and final
    print(f"\nComparison:")
    print(f"Original lines: {len(original_text.splitlines())}")
    print(f"Final lines: {len(final_text.splitlines())}")
    
    if original_text.strip() == final_text.strip():
        print("✅ Full cycle preserves content!")
    else:
        print("❌ Content changed during conversion cycle")
        print("Differences:")
        orig_lines = original_text.strip().splitlines()
        final_lines = final_text.strip().splitlines()
        for i, (orig, final) in enumerate(zip(orig_lines, final_lines)):
            if orig != final:
                print(f"  Line {i+1}: '{orig}' -> '{final}'")


def main():
    """Run all conversion tests."""
    print("Document Conversion <br> Tag Test Suite")
    print("=" * 50)
    
    try:
        # Test individual components
        test_plain_text_to_html_conversion()
        test_docx_content_conversion()
        test_html_to_plain_text_conversion()
        test_full_conversion_cycle()
        
        print("\n" + "=" * 50)
        print("Test suite completed!")
        print("\nIf you see ✅ for all tests, the <br> tag conversion is working correctly.")
        print("If you see ❌ for any tests, there are still issues to fix.")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        logger.error(f"Test suite error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    # Configure logging for testing
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
