#!/usr/bin/env python3
"""
Test script to verify direct image embedding without UUID markers.

This script tests that images are embedded directly as data URIs in HTML
instead of using intermediate UUID markers that can fail to be replaced.
"""

import sys
import tempfile
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

# Create a minimal test image (1x1 pixel PNG)
TEST_IMAGE_DATA = base64.b64decode(
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
)


def create_test_docx_with_images():
    """Create a test DOCX document with embedded images."""
    try:
        from docx import Document
        import io
        
        # Create a new document
        doc = Document()
        
        # Add title
        title = doc.add_heading('Test Document with Direct Image Embedding', 0)
        
        # Add first paragraph
        para1 = doc.add_paragraph('This is the first paragraph before the image.')
        
        # Add first image
        image_stream = io.BytesIO(TEST_IMAGE_DATA)
        doc.add_picture(image_stream, width=None)  # Use original size
        
        # Add paragraph after first image
        para2 = doc.add_paragraph('This paragraph comes after the first image.')
        
        # Add heading
        doc.add_heading('Section with Another Image', level=1)
        
        # Add content
        para3 = doc.add_paragraph('Here is some content before the second image.')
        
        # Add second image
        image_stream2 = io.BytesIO(TEST_IMAGE_DATA)
        doc.add_picture(image_stream2, width=None)  # Use original size
        
        # Add final paragraph
        para4 = doc.add_paragraph('This is the final paragraph after all images.')
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
        doc.save(temp_file.name)
        temp_file.close()
        
        return Path(temp_file.name)
        
    except ImportError:
        logger.warning("python-docx not available, cannot create test DOCX")
        return None
    except Exception as e:
        logger.error(f"Error creating test DOCX: {e}")
        return None


def test_direct_image_embedding():
    """Test the direct image embedding approach."""
    print("=== Testing Direct Image Embedding ===")
    
    # Create test document
    test_docx = create_test_docx_with_images()
    if not test_docx:
        print("❌ Could not create test DOCX document")
        return False
    
    try:
        print(f"Created test DOCX: {test_docx}")
        
        # Test the image-aware document service
        from document_manager.services.image_aware_document_service import ImageAwareDocumentService
        
        image_service = ImageAwareDocumentService()
        text_content, images = image_service.extract_content_with_images(test_docx)
        
        print(f"Extracted text content ({len(text_content)} chars)")
        print("First 500 chars of content:")
        print(text_content[:500] + "..." if len(text_content) > 500 else text_content)
        
        print(f"\nExtracted {len(images)} images:")
        for i, img in enumerate(images):
            print(f"  Image {i+1}: {img.width}x{img.height}")
            print(f"    Alt text: {img.alt_text}")
            print(f"    Data URI length: {len(img.data_uri)} chars")
        
        # Check for direct image embedding
        print("\n--- Analysis ---")
        
        # Count UUID markers (should be 0 with direct embedding)
        uuid_markers = text_content.count('[IMAGE:')
        print(f"UUID image markers: {uuid_markers}")
        
        # Count HTML img tags (should match number of images)
        html_img_tags = text_content.count('<img src=')
        print(f"HTML img tags: {html_img_tags}")
        
        # Count data URI images
        data_uri_images = text_content.count('data:image/')
        print(f"Data URI images: {data_uri_images}")
        
        # Check for specific patterns
        if uuid_markers == 0:
            print("✅ No UUID markers found - direct embedding working")
        else:
            print(f"❌ Found {uuid_markers} UUID markers - direct embedding failed")
        
        if html_img_tags > 0:
            print(f"✅ Found {html_img_tags} HTML img tags")
        else:
            print("❌ No HTML img tags found")
        
        if data_uri_images > 0:
            print(f"✅ Found {data_uri_images} data URI images")
        else:
            print("❌ No data URI images found")
        
        # Test HTML generation
        print("\n--- Testing HTML Generation ---")
        html_content = image_service.create_html_with_embedded_images(text_content, images)
        print(f"Generated HTML ({len(html_content)} chars)")
        
        # Check HTML content
        html_img_count = html_content.count('<img src=')
        html_data_uri_count = html_content.count('data:image/')
        
        print(f"HTML img tags in final HTML: {html_img_count}")
        print(f"Data URI images in final HTML: {html_data_uri_count}")
        
        # Show a sample of the HTML
        print("\nSample HTML content:")
        if '<img ' in html_content:
            # Find and show the first image tag
            img_start = html_content.find('<img ')
            img_end = html_content.find('>', img_start) + 1
            if img_end > img_start:
                img_tag = html_content[img_start:img_end]
                print(f"First img tag: {img_tag[:100]}...")
        
        # Overall assessment
        success = (uuid_markers == 0 and html_img_tags > 0 and data_uri_images > 0)
        
        if success:
            print("\n✅ Direct image embedding is working correctly!")
            print("Images are embedded as data URIs without UUID markers")
        else:
            print("\n❌ Direct image embedding has issues")
            print("Check the analysis above for specific problems")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Test error: {e}")
        return False
    
    finally:
        # Clean up
        if test_docx and test_docx.exists():
            test_docx.unlink()


def test_conversion_service_integration():
    """Test the full conversion service with direct embedding."""
    print("\n=== Testing Conversion Service Integration ===")
    
    # Create test document
    test_docx = create_test_docx_with_images()
    if not test_docx:
        print("❌ Could not create test DOCX document")
        return False
    
    try:
        print(f"Testing conversion service with: {test_docx}")
        
        # Test the full conversion service
        from document_manager.services.document_conversion_service import DocumentConversionService
        
        service = DocumentConversionService()
        doc_format = service.convert_file_to_document_format(
            test_docx,
            document_name="Test Document with Direct Images",
            preserve_formatting=True
        )
        
        if doc_format:
            print("✅ Conversion successful")
            print(f"Document name: {doc_format.name}")
            print(f"Has images: {doc_format.metadata.get('has_images', False)}")
            print(f"Image count: {doc_format.metadata.get('image_count', 0)}")
            
            # Check the HTML content
            html_content = doc_format.html_content
            
            # Count UUID markers (should be 0)
            uuid_markers = html_content.count('[IMAGE:')
            print(f"UUID markers in final HTML: {uuid_markers}")
            
            # Count img tags
            img_tags = html_content.count('<img src=')
            print(f"IMG tags in final HTML: {img_tags}")
            
            # Count data URIs
            data_uris = html_content.count('data:image/')
            print(f"Data URIs in final HTML: {data_uris}")
            
            if uuid_markers == 0 and img_tags > 0:
                print("✅ Conversion service produces clean HTML with embedded images")
                return True
            else:
                print("❌ Conversion service still has UUID marker issues")
                
                # Show problematic content
                if uuid_markers > 0:
                    lines = html_content.split('\n')
                    for i, line in enumerate(lines):
                        if '[IMAGE:' in line:
                            print(f"Problematic line {i}: {line}")
                            break
                
                return False
        else:
            print("❌ Conversion failed")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        logger.error(f"Integration test error: {e}")
        return False
    
    finally:
        # Clean up
        if test_docx and test_docx.exists():
            test_docx.unlink()


def main():
    """Run all direct embedding tests."""
    print("Direct Image Embedding Test Suite")
    print("=" * 50)
    
    try:
        # Test direct embedding
        embedding_test = test_direct_image_embedding()
        
        # Test full integration
        integration_test = test_conversion_service_integration()
        
        print("\n" + "=" * 50)
        print("Test Results:")
        print(f"Direct embedding: {'✅ PASS' if embedding_test else '❌ FAIL'}")
        print(f"Service integration: {'✅ PASS' if integration_test else '❌ FAIL'}")
        
        if embedding_test and integration_test:
            print("\n🎉 SUCCESS! Direct image embedding is working!")
            print("\nImages should now appear correctly in the RTF Editor")
            print("without any UUID markers like [IMAGE:uuid]")
        else:
            print("\n❌ Some tests failed - direct embedding needs more work")
        
        return 0 if (embedding_test and integration_test) else 1
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}")
        return 1


if __name__ == "__main__":
    # Configure logging for testing
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG"
    )
    
    exit_code = main()
    sys.exit(exit_code)
