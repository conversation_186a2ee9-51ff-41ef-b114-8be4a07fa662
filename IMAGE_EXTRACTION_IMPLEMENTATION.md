# Image Extraction and Positioning Implementation

## Overview

This implementation adds comprehensive image extraction and positioning preservation to the document conversion system. Images from PDF and DOCX files are now extracted, embedded in HTML with proper positioning, and displayed correctly in the RTF Editor.

## Key Components

### 1. ImageAwareDocumentService (`document_manager/services/image_aware_document_service.py`)

**Purpose**: Extracts text content and images from documents while preserving their relative positioning.

**Key Features**:
- **PDF Image Extraction**: Uses PyMuPDF to extract images from PDF pages
- **DOCX Image Extraction**: Extracts embedded images from Word documents
- **Position Preservation**: Maintains image positions relative to text content
- **Data URI Conversion**: Converts images to base64 data URIs for embedding
- **HTML Generation**: Creates HTML with properly positioned images

**Classes**:
- `ImageInfo`: Stores image data, dimensions, position, and metadata
- `ImageAwareDocumentService`: Main service for image-aware document processing

### 2. Enhanced DocumentConversionService

**New Features**:
- **Automatic Image Detection**: Detects PDF/DOCX files and enables image extraction
- **Dual Processing Modes**: 
  - Image-aware mode for PDF/DOCX with `preserve_formatting=True`
  - Text-only mode for other formats or when images are disabled
- **Metadata Enhancement**: Includes image count and extraction status in document metadata

### 3. Image Processing Pipeline

```
Document File (PDF/DOCX)
    ↓
Extract Text + Images with Positions
    ↓
Convert Images to Data URIs
    ↓
Insert Image Markers in Text
    ↓
Generate HTML with Embedded Images
    ↓
RTF Editor Display
```

## Technical Implementation

### PDF Image Extraction

```python
# Extract images from PDF pages
for page_num in range(len(doc)):
    page = doc[page_num]
    image_list = page.get_images()
    
    for img_index, img in enumerate(image_list):
        xref = img[0]
        pix = fitz.Pixmap(doc, xref)
        img_data = pix.tobytes("png")
        data_uri = f"data:image/png;base64,{base64.b64encode(img_data).decode()}"
```

### DOCX Image Extraction

```python
# Extract images from DOCX runs
for run in para.runs:
    for inline_shape in run.element.xpath('.//a:blip'):
        embed_id = inline_shape.get('{...}embed')
        image_part = doc.part.related_parts[embed_id]
        image_data = image_part.blob
        data_uri = f"data:image/{format};base64,{base64.b64encode(image_data).decode()}"
```

### HTML Image Embedding

```python
# Replace image markers with HTML img tags
if line.startswith('[IMAGE:') and line.endswith(']'):
    image_id = line[7:-1]
    img_info = image_map[image_id]
    
    img_html = f'''<div style="text-align: center; margin: 10px 0;">
        <img src="{img_info.data_uri}" 
             alt="{img_info.alt_text}" 
             style="max-width: {max_width}px; max-height: {max_height}px;" />
    </div>'''
```

## Image Positioning Strategy

### 1. Position Tracking
- **Character-based Positioning**: Images are positioned based on character offsets in text
- **Marker System**: `[IMAGE:id]` markers are inserted at appropriate text positions
- **Relative Positioning**: Images maintain their position relative to surrounding text

### 2. HTML Layout
- **Centered Display**: Images are centered in their own div containers
- **Responsive Sizing**: Images are constrained to maximum dimensions while preserving aspect ratio
- **Proper Spacing**: Margins ensure proper separation from text content

### 3. RTF Editor Integration
- **Data URI Support**: RTF Editor's existing image system handles data URIs
- **Automatic Sizing**: Images are automatically sized appropriately for display
- **Positioning Preservation**: Original document layout is maintained

## Configuration and Limits

### Size Limits
- **Maximum Image Dimensions**: 800px width, 600px height (display limits)
- **HTML Content Limit**: 2MB total HTML size
- **File Size Limit**: 50MB maximum input file size

### Format Support
- **PDF**: Full image extraction with PyMuPDF
- **DOCX**: Embedded image extraction with python-docx
- **Fallback**: Text-only extraction for unsupported formats

### Dependencies
- **PyMuPDF (fitz)**: PDF processing and image extraction
- **python-docx**: DOCX document processing
- **Pillow (PIL)**: Image dimension detection and processing
- **base64**: Image data encoding

## Usage Examples

### Basic Conversion with Images
```python
service = DocumentConversionService()
doc_format = service.convert_file_to_document_format(
    "document_with_images.pdf",
    preserve_formatting=True  # Enables image extraction
)
```

### Direct Image Extraction
```python
image_service = ImageAwareDocumentService()
text_content, images = image_service.extract_content_with_images("document.pdf")
html_content = image_service.create_html_with_embedded_images(text_content, images)
```

## Testing

### Test Script (`test_image_extraction.py`)
- **Service Testing**: Validates ImageAwareDocumentService functionality
- **Conversion Testing**: Tests enhanced DocumentConversionService
- **Format Support**: Verifies supported format detection
- **HTML Generation**: Tests image embedding in HTML

### Manual Testing Steps
1. **Prepare Test Documents**: Use PDF/DOCX files with embedded images
2. **Convert Documents**: Use Document Conversion dialog with formatting preservation
3. **Import to RTF Editor**: Verify images appear with correct positioning
4. **Check Metadata**: Confirm image count and extraction status in document metadata

## Error Handling

### Graceful Degradation
- **Missing Dependencies**: Falls back to text-only extraction
- **Image Extraction Failures**: Continues processing other images
- **Large Content**: Automatic truncation with user notification
- **Unsupported Formats**: Seamless fallback to existing text extraction

### Logging and Debugging
- **Detailed Logging**: Comprehensive logging of extraction process
- **Error Context**: Specific error information for troubleshooting
- **Performance Metrics**: Timing and memory usage tracking

## Benefits

### For Users
- **Complete Document Preservation**: Images are preserved with original positioning
- **Professional Quality**: Converted documents maintain visual fidelity
- **Seamless Workflow**: Automatic image handling without user intervention

### For Developers
- **Modular Design**: Separate image service for easy maintenance
- **Extensible Architecture**: Easy to add support for new formats
- **Robust Error Handling**: Graceful degradation ensures system stability

## Future Enhancements

### Potential Improvements
- **Image Optimization**: Automatic image compression for better performance
- **Format Support**: Additional formats (PowerPoint, OpenDocument, etc.)
- **Advanced Positioning**: More sophisticated layout preservation
- **Image Editing**: Basic image editing capabilities in RTF Editor

### Performance Optimizations
- **Lazy Loading**: Load images on demand for large documents
- **Caching**: Cache extracted images for repeated access
- **Parallel Processing**: Multi-threaded image extraction for large documents

The image extraction system provides a comprehensive solution for preserving document fidelity during conversion, ensuring that users can work with complete documents including all visual elements in their original positions.
