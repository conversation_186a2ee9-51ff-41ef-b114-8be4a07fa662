# Fix for Image Markers Appearing as Literal Text

## Problem Identified

When importing documents with images through the Document Conversion system, users were seeing literal text like `[IMAGE:e81fe340-0201-4bbe-a833-45dcafb3fc10]` instead of actual images in the RTF Editor.

## Root Cause Analysis

The issue was traced through the complete document processing pipeline:

1. ✅ **Image Extraction**: Images were being correctly extracted from PDF/DOCX files
2. ✅ **Image Markers**: Image markers `[IMAGE:id]` were being correctly inserted into text content
3. ✅ **HTML Generation**: The image-aware service was correctly generating HTML with `<img>` tags
4. ❌ **HTML Loading**: The RTF Editor was converting HTML back to plain text, stripping out `<img>` tags

### The Specific Issue

In `document_manager/ui/document_tab.py`, the `_load_html_safely` method had logic that:

1. **For content > 50KB**: Converted HTML to plain text to prevent UI freezing
2. **HTML-to-plain-text conversion**: Stripped ALL HTML tags, including `<img>` tags
3. **Result**: Only the `[IMAGE:id]` markers remained as literal text

## Solution Implemented

### 1. Modified HTML Loading Logic (`_load_html_safely` method)

**Before:**
```python
# For content > 50KB, always convert to plain text
if len(html_content) < 50000:
    editor.text_edit.setHtml(html_content)  # Load as HTML
else:
    plain_text = self._html_to_plain_text(html_content)  # Strip all HTML
    editor.text_edit.setPlainText(plain_text)
```

**After:**
```python
# Check if content contains images
has_images = '<img ' in html_content.lower()

# For smaller content OR content with images, load directly as HTML
if len(html_content) < 100000 or has_images:  # Increased threshold, always preserve images
    editor.text_edit.setHtml(html_content)  # Load as HTML with images
else:
    plain_text = self._html_to_plain_text(html_content)  # Only for large text-only content
    editor.text_edit.setPlainText(plain_text)
```

### 2. Enhanced HTML-to-Plain-Text Conversion

**Before:**
```python
# Remove all HTML tags
html_content = re.sub(r'<[^>]+>', '', html_content)
```

**After:**
```python
# Handle images specially - convert to placeholder text
html_content = re.sub(
    r'<img[^>]*alt=["\']([^"\']*)["\'][^>]*>',
    r'\n[IMAGE: \1]\n',
    html_content,
    flags=re.IGNORECASE
)
# Handle images without alt text
html_content = re.sub(
    r'<img[^>]*>',
    r'\n[IMAGE]\n',
    html_content,
    flags=re.IGNORECASE
)

# Remove all remaining HTML tags (except images which are already handled)
html_content = re.sub(r'<[^>]+>', '', html_content)
```

## Key Changes Made

### File: `document_manager/ui/document_tab.py`

1. **Line 881**: Added image detection: `has_images = '<img ' in html_content.lower()`

2. **Line 884**: Modified loading condition:
   - **Old**: `if len(html_content) < 50000:`
   - **New**: `if len(html_content) < 100000 or has_images:`

3. **Lines 962-974**: Enhanced image handling in HTML-to-plain-text conversion:
   - Convert `<img>` tags to readable placeholders like `[IMAGE: Alt Text]`
   - Preserve image information even when HTML is converted to plain text

## Benefits of the Fix

### 1. Images Display Correctly
- ✅ Documents with images are loaded as HTML, preserving `<img>` tags
- ✅ Images appear as actual images in the RTF Editor
- ✅ No more literal `[IMAGE:id]` text

### 2. Performance Optimization
- ✅ Increased size threshold from 50KB to 100KB for better user experience
- ✅ Smart loading: Images always load as HTML regardless of size
- ✅ Large text-only documents still convert to plain text for performance

### 3. Graceful Degradation
- ✅ If HTML-to-plain-text conversion is needed, images become readable placeholders
- ✅ Users see `[IMAGE: Description]` instead of cryptic UUIDs
- ✅ System remains stable for very large documents

## Testing Verification

### Test Results
All tests pass, confirming:
- ✅ **HTML to plain text conversion**: Preserves image information as readable placeholders
- ✅ **Image detection logic**: Correctly identifies HTML content with images
- ✅ **Size threshold logic**: Properly handles different content sizes and image presence

### Manual Testing
1. **Convert a PDF/DOCX with images** using Document Conversion
2. **Enable "Preserve Formatting"** to activate image extraction
3. **Import into RTF Editor** - images should now display correctly
4. **No more `[IMAGE:uuid]` text** should appear

## Impact

### Before Fix
- ❌ Images appeared as literal `[IMAGE:e81fe340-0201-4bbe-a833-45dcafb3fc10]` text
- ❌ Poor user experience with converted documents
- ❌ Loss of visual information from original documents

### After Fix
- ✅ Images display correctly in RTF Editor
- ✅ Professional-quality document conversion
- ✅ Complete preservation of document visual content
- ✅ Improved user experience

## Technical Details

### Image Processing Pipeline (Fixed)
```
1. Document File (PDF/DOCX)
   ↓
2. Extract Images + Text with Positioning
   ↓
3. Generate HTML with <img> tags
   ↓
4. Detect Images in HTML ← NEW
   ↓
5. Load as HTML (preserving images) ← FIXED
   ↓
6. Display in RTF Editor with Images ← SUCCESS
```

### Fallback Behavior
- **Large documents with images**: Still load as HTML to preserve images
- **Very large text-only documents**: Convert to plain text for performance
- **Image placeholders**: Readable `[IMAGE: Description]` instead of UUIDs

The fix ensures that the image extraction and positioning system works end-to-end, providing users with complete document fidelity including all visual elements in their original positions.
